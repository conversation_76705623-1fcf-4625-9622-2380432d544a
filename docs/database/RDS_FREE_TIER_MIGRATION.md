# RDS免费套餐迁移指南

## 概述

本文档描述了如何将开发环境从Aurora Serverless v2迁移到RDS免费套餐，以优化成本并充分利用AWS免费套餐资源。

## 更改摘要

### 架构变更

| 环境 | 原架构 | 新架构 | 原因 |
|------|--------|--------|------|
| dev | Aurora Serverless v2 | RDS PostgreSQL (db.t4g.micro) | 利用免费套餐，降低开发成本 |
| prod | Aurora Serverless v2 | Aurora Serverless v2 | 保持生产环境高可用性 |

### 技术规格对比

#### 开发环境 (dev)

| 项目 | Aurora Serverless v2 | RDS免费套餐 |
|------|---------------------|-------------|
| 实例类型 | Serverless (0.5-1 ACU) | db.t4g.micro |
| CPU | 可变 | 2 vCPU |
| 内存 | 可变 | 1 GB |
| 存储 | 按需扩展 | 20 GB (固定) |
| 多可用区 | 支持 | 单可用区 |
| 读副本 | 支持 | 不支持 |
| 性能洞察 | 可选 | 关闭 |
| 增强监控 | 可选 | 关闭 |
| 月费用 | ~$50-100 | $0 (免费套餐) |

#### 生产环境 (prod)

保持不变，继续使用Aurora Serverless v2以确保高可用性和性能。

## 免费套餐限制

### AWS RDS免费套餐包含：

- **实例时间**: 每月750小时的db.t3.micro或db.t4g.micro实例
- **存储**: 20 GB通用SSD存储
- **备份**: 20 GB备份存储
- **I/O**: 每月1000万I/O请求
- **数据传输**: 每月15 GB数据传输

### 重要限制：

1. **单实例**: 免费套餐不支持多可用区部署
2. **存储限制**: 最大20GB，超出将产生费用
3. **时间限制**: 每月750小时，约31天×24小时
4. **区域限制**: 免费套餐在所有区域可用
5. **账户限制**: 仅适用于AWS账户创建后的12个月内

## 代码更改详情

### 1. 数据库栈更改

**文件**: `lib/stacks/aurora-database-stack.ts`

主要更改：
- 添加环境检测逻辑
- dev环境使用`rds.DatabaseInstance`
- prod环境继续使用`rds.DatabaseCluster`
- 统一端点接口以保持兼容性

### 2. 配置更改

**文件**: `lib/config/stacks/database.ts`

添加了RDS实例配置接口：
```typescript
rdsInstanceConfig?: {
  instanceClass: 'db.t3.micro' | 'db.t4g.micro';
  allocatedStorage: number;
  maxAllocatedStorage: number;
  storageType: 'gp2' | 'gp3';
  multiAz: boolean;
};
```

**文件**: `lib/config/environments/dev.ts`

更新dev环境配置：
- 引擎类型: `aurora-postgresql` → `rds-postgresql`
- 添加RDS实例配置
- 关闭性能洞察和增强监控

### 3. 应用栈兼容性

**文件**: `lib/stacks/application-stack.ts`

更新数据库连接逻辑：
- 使用统一的`clusterEndpoint`属性
- 自动解析主机地址和端口

## 部署步骤

### 1. 准备工作

确保AWS CLI已配置并有适当权限：
```bash
aws sts get-caller-identity
```

### 2. 执行部署

使用提供的部署脚本：
```bash
./scripts/deploy-dev-rds-free-tier.sh
```

或手动部署：
```bash
export NODE_ENV=dev
npm install
npm run build
npx cdk deploy YuanhuiAuroraDatabase-dev --require-approval never
```

### 3. 数据库初始化

部署完成后，初始化数据库：

```bash
# 获取数据库密码
DB_SECRET_ARN=$(aws cloudformation describe-stacks \
  --stack-name YuanhuiAuroraDatabase-dev \
  --query 'Stacks[0].Outputs[?OutputKey==`DatabaseSecretArn`].OutputValue' \
  --output text)

aws secretsmanager get-secret-value \
  --secret-id $DB_SECRET_ARN \
  --query SecretString --output text

# 连接数据库
DB_ENDPOINT=$(aws cloudformation describe-stacks \
  --stack-name YuanhuiAuroraDatabase-dev \
  --query 'Stacks[0].Outputs[?OutputKey==`DatabaseEndpoint`].OutputValue' \
  --output text)

psql -h ${DB_ENDPOINT%:*} -p 5432 -U odoo_admin -d postgres
```

创建应用数据库：
```sql
CREATE DATABASE yherp;
CREATE DATABASE khmall;
CREATE DATABASE airflow;
```

## 监控和维护

### 1. 免费套餐使用监控

定期检查免费套餐使用情况：
```bash
# 查看账单仪表板
aws ce get-usage-and-costs \
  --time-period Start=2025-01-01,End=2025-01-31 \
  --granularity MONTHLY \
  --metrics BlendedCost \
  --group-by Type=DIMENSION,Key=SERVICE
```

### 2. 存储监控

监控数据库存储使用：
```bash
aws rds describe-db-instances \
  --db-instance-identifier $(aws rds describe-db-instances \
    --query 'DBInstances[?DBName==`odoo`].DBInstanceIdentifier' \
    --output text) \
  --query 'DBInstances[0].{AllocatedStorage:AllocatedStorage,StorageType:StorageType,MultiAZ:MultiAZ}'
```

### 3. 性能监控

虽然关闭了性能洞察，仍可使用基本CloudWatch指标：
```bash
aws cloudwatch get-metric-statistics \
  --namespace AWS/RDS \
  --metric-name CPUUtilization \
  --dimensions Name=DBInstanceIdentifier,Value=<instance-id> \
  --start-time $(date -u -d '1 hour ago' +%Y-%m-%dT%H:%M:%S) \
  --end-time $(date -u +%Y-%m-%dT%H:%M:%S) \
  --period 300 \
  --statistics Average
```

## 故障排除

### 1. 连接问题

如果应用无法连接到数据库：

1. 检查安全组配置
2. 验证数据库状态
3. 确认端点地址正确

### 2. 存储空间不足

如果接近20GB限制：

1. 清理不必要的数据
2. 优化数据库表
3. 考虑升级到付费实例

### 3. 性能问题

如果遇到性能问题：

1. 检查CPU和内存使用率
2. 优化数据库查询
3. 考虑添加索引
4. 评估是否需要升级实例类型

## 成本优化建议

### 1. 开发最佳实践

- 定期清理测试数据
- 使用数据库连接池
- 优化查询性能
- 监控存储使用

### 2. 免费套餐最大化

- 确保实例运行时间不超过750小时/月
- 监控I/O使用情况
- 合理使用备份功能
- 定期检查账单

### 3. 升级路径

当需要更多资源时的升级选项：
1. **db.t4g.small**: 2 vCPU, 2 GB RAM
2. **db.t4g.medium**: 2 vCPU, 4 GB RAM
3. **Aurora Serverless v2**: 按需扩展

## 回滚计划

如果需要回滚到Aurora Serverless v2：

1. 更新配置文件中的引擎类型
2. 重新部署数据库栈
3. 迁移数据（如需要）
4. 更新应用配置

## 总结

通过迁移到RDS免费套餐，开发环境可以：

✅ **节省成本**: 每月节省$50-100的数据库费用  
✅ **简化架构**: 单实例部署，减少复杂性  
✅ **保持功能**: 核心数据库功能完全可用  
✅ **易于管理**: 标准RDS管理界面和工具  

⚠️ **注意限制**: 存储和性能限制，适合开发和测试环境

这种架构为开发环境提供了成本效益的解决方案，同时保持生产环境的高可用性和性能。
