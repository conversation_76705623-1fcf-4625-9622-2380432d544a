# AWS CDK SQL执行方案

## 概述

本项目实现了一个基于AWS CDK Custom Resource和Lambda函数的通用SQL执行方案，用于在AWS RDS/Aurora数据库中执行SQL操作。该方案满足以下要求：

- ✅ **Lambda运行时环境**: Python 3.12
- ✅ **依赖管理**: 不使用Lambda Layers，直接在函数代码中包含依赖
- ✅ **实现方式**: CDK Custom Resource + Lambda函数
- ✅ **适用场景**: 数据库初始化、表创建、用户权限设置等SQL操作
- ✅ **集成要求**: 与现有Aurora Serverless v2数据库配置集成
- ✅ **权限配置**: 最小权限原则的IAM权限配置
- ✅ **错误处理**: 完善的错误处理和日志记录机制
- ✅ **部署优化**: 简单的部署结构，避免复杂的依赖层管理

## 核心组件

### 1. SqlExecutorConstruct
**位置**: `lib/constructs/sql-executor-construct.ts`

通用SQL执行构造，提供以下功能：
- Python 3.12 Lambda函数
- RDS Data API和直接连接双重支持
- 完善的IAM权限配置
- SQL命令安全性验证
- 详细的错误处理和日志记录

### 2. 集成示例
**位置**: `lib/stacks/aurora-database-stack.ts`

在现有Aurora数据库栈中集成SQL执行构造的实际示例，展示如何：
- 创建数据库扩展
- 设置应用schema
- 创建审计表和配置表
- 插入默认配置数据

### 3. 使用文档
**位置**: `docs/sql-executor-guide.md`

详细的使用指南，包含：
- 完整的API文档
- 多种使用场景示例
- 配置参数说明
- 故障排除指南
- 最佳实践建议

### 4. 示例代码
**位置**: `examples/sql-executor-examples.ts`

完整的使用示例栈，展示：
- 数据库基础设施初始化
- 用户权限管理
- 核心业务表创建
- 审计日志系统
- 数据库函数和触发器

## 技术特性

### Lambda函数配置
```typescript
runtime: lambda.Runtime.PYTHON_3_12
timeout: 15分钟
memorySize: 512MB
vpc: 私有子网部署
logRetention: 1周
```

### IAM权限（最小权限原则）
- **Secrets Manager**: 读取数据库凭证（带环境标签条件）
- **RDS Data API**: 执行SQL命令（Aurora Serverless）
- **CloudWatch Logs**: 日志记录
- **STS**: 获取账户信息

### 安全特性
- SQL命令安全性验证
- 危险关键字检测
- 命令长度限制
- 网络隔离（VPC私有子网）
- 资源标签条件访问控制

### 错误处理
- 详细的错误日志记录
- 敏感信息过滤
- 执行摘要统计
- 失败命令继续执行策略

## 使用方法

### 基本用法
```typescript
import { SqlExecutorConstruct } from '../constructs/sql-executor-construct';

const sqlExecutor = new SqlExecutorConstruct(this, 'DatabaseSetup', {
  config: environmentConfig,
  vpc: vpc,
  databaseCluster: auroraCluster,
  databaseSecret: databaseSecret,
  databaseSecurityGroup: databaseSecurityGroup,
  resourceName: 'my-database-setup',
  description: 'Initialize database schema',
  sqlCommands: [
    'CREATE EXTENSION IF NOT EXISTS "uuid-ossp";',
    'CREATE SCHEMA IF NOT EXISTS app_data;',
    'CREATE TABLE IF NOT EXISTS app_data.users (id UUID PRIMARY KEY);',
  ],
});
```

### 部署步骤
```bash
# 1. 编译项目
npm run build

# 2. 部署数据库栈（包含SQL执行示例）
npx cdk deploy YuanhuiAuroraDatabase-dev --require-approval never

# 3. 查看执行日志
aws logs tail /aws/lambda/*sql-executor* --follow
```

## 执行模式

### 1. RDS Data API模式（推荐）
- **适用于**: Aurora Serverless v2集群
- **优势**: 无需管理连接，自动连接池
- **使用条件**: 设置CLUSTER_IDENTIFIER环境变量

### 2. 直接连接模式
- **适用于**: 标准RDS实例
- **要求**: psycopg2库（当前版本会提示导入错误）
- **建议**: 生产环境使用RDS Data API

## 监控和调试

### CloudWatch日志
```bash
# 查看执行日志
aws logs tail /aws/lambda/YuanhuiAuroraDatabase-dev-SqlExecutorExample --follow

# 查看特定时间段日志
aws logs filter-log-events \
  --log-group-name /aws/lambda/YuanhuiAuroraDatabase-dev-SqlExecutorExample \
  --start-time 1640995200000
```

### 关键指标
- SQL命令执行成功率
- 执行时间和性能
- 错误类型和频率
- 安全警告统计

## 故障排除

### 常见问题

1. **权限错误**
   - 检查IAM权限配置
   - 验证资源标签设置
   - 确认环境变量正确

2. **网络连接问题**
   - 检查安全组规则
   - 验证Lambda子网配置
   - 确认数据库端点可达

3. **SQL执行失败**
   - 检查SQL语法正确性
   - 验证表/schema存在
   - 确认执行顺序依赖

### 调试命令
```bash
# 测试RDS Data API连接
aws rds-data execute-statement \
  --resource-arn <cluster-arn> \
  --secret-arn <secret-arn> \
  --database postgres \
  --sql "SELECT version();"

# 验证IAM权限
aws iam simulate-principal-policy \
  --policy-source-arn <lambda-role-arn> \
  --action-names secretsmanager:GetSecretValue \
  --resource-arns <secret-arn>
```

## 项目集成

### 现有集成
该SQL执行方案已集成到以下项目组件：

1. **Aurora数据库栈** (`lib/stacks/aurora-database-stack.ts`)
   - 自动创建数据库扩展
   - 设置应用schema
   - 创建系统配置表

2. **环境配置** (`lib/config/`)
   - 支持多环境部署
   - 环境特定的SQL命令

3. **IAM权限** (`lib/stacks/security-stack.ts`)
   - 最小权限原则
   - 资源标签条件控制

### 扩展建议

1. **数据迁移支持**
   - 版本化SQL脚本
   - 回滚机制
   - 迁移状态跟踪

2. **批量操作优化**
   - 大量SQL命令分批执行
   - 并行执行支持
   - 进度监控

3. **模板化SQL**
   - 参数化SQL模板
   - 环境变量替换
   - 动态SQL生成

## 文件结构

```
├── lib/constructs/
│   └── sql-executor-construct.ts          # 核心SQL执行构造
├── lib/stacks/
│   └── aurora-database-stack.ts           # 集成示例
├── docs/
│   ├── sql-executor-guide.md              # 详细使用指南
│   └── sql-executor-README.md             # 本文件
├── examples/
│   └── sql-executor-examples.ts           # 完整使用示例
└── README.md                              # 项目主文档
```

## 相关文档

- [详细使用指南](./sql-executor-guide.md)
- [使用示例代码](../examples/sql-executor-examples.ts)
- [项目架构文档](./architecture/README.md)
- [AWS RDS Data API文档](https://docs.aws.amazon.com/AmazonRDS/latest/AuroraUserGuide/data-api.html)

## 支持

如有问题或建议，请：
1. 查看[故障排除指南](./sql-executor-guide.md#故障排除)
2. 检查CloudWatch日志
3. 提交Issue或联系技术支持

---

**注意**: 该方案已在开发环境中测试验证，建议在生产环境部署前进行充分测试。
