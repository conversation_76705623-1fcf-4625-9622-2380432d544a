# 元晖网络安全配置最佳实践

## 概述

本文档详细说明了元晖网络基础设施的安全配置最佳实践，涵盖网络安全、应用安全、数据安全和访问控制等多个方面。

## 网络安全

### 1. VPC网络隔离

#### 子网分层设计
```
公网子网 (10.0.1.0/24, 10.0.2.0/24)
├── 公网ALB
└── NAT网关

私有子网 (10.0.11.0/24, 10.0.12.0/24)
├── ECS应用服务
├── 内部ALB
└── OpenZiti服务

数据库子网 (*********/28, *********/28)
├── PostgreSQL集群
└── Redis缓存
```

#### 安全组配置原则

**公网ALB安全组**
```bash
# 入站规则
HTTP (80)    0.0.0.0/0    # 重定向到HTTPS
HTTPS (443)  0.0.0.0/0    # 公网HTTPS访问

# 出站规则
ALL          ECS安全组     # 仅允许访问ECS服务
```

**内部ALB安全组**
```bash
# 入站规则
HTTP (80)    VPC CIDR     # VPC内部HTTP访问
HTTPS (443)  VPC CIDR     # VPC内部HTTPS访问
HTTP (80)    企业IP段      # 企业网络访问
HTTPS (443)  企业IP段      # 企业网络访问

# 出站规则
ALL          ECS安全组     # 仅允许访问ECS服务
```

**ECS应用安全组**
```bash
# 入站规则
8069         公网ALB安全组  # 来自公网ALB的流量
8069         内部ALB安全组  # 来自内部ALB的流量
ALL          ECS安全组     # 服务间通信

# 出站规则
5434         数据库安全组   # 访问PgPool
6379         Redis安全组   # 访问Redis
443          0.0.0.0/0    # HTTPS出站（API调用等）
80           0.0.0.0/0    # HTTP出站（更新等）
```

### 2. WAF防护配置

#### AWS托管规则集
```typescript
// 核心规则集 - 防护常见攻击
AWSManagedRulesCommonRuleSet

// 已知恶意输入规则集
AWSManagedRulesKnownBadInputsRuleSet

// SQL注入防护规则集
AWSManagedRulesSQLiRuleSet

// Linux操作系统规则集
AWSManagedRulesLinuxRuleSet

// POSIX操作系统规则集
AWSManagedRulesPOSIXRuleSet
```

#### 自定义规则配置
```typescript
// IP白名单规则
{
  name: 'IPWhitelistRule',
  priority: 10,
  action: { allow: {} },
  statement: {
    ipSetReferenceStatement: {
      arn: 'arn:aws:wafv2:region:account:regional/ipset/whitelist'
    }
  }
}

// 地理位置阻止规则
{
  name: 'GeoBlockingRule',
  priority: 20,
  action: { block: {} },
  statement: {
    geoMatchStatement: {
      countryCodes: ['CN', 'RU', 'KP']  // 根据业务需求调整
    }
  }
}

// 速率限制规则
{
  name: 'RateLimitRule',
  priority: 30,
  action: { block: {} },
  statement: {
    rateBasedStatement: {
      limit: 2000,
      aggregateKeyType: 'IP'
    }
  }
}
```

### 3. OpenZiti零信任网络

#### 网络策略配置
```bash
# 创建服务策略
ziti edge create service yherp-internal \
  --configs yherp-intercept-config,yherp-host-config

# 创建身份
ziti edge create identity user employee-001 \
  --role-attributes employee,internal

# 创建服务策略
ziti edge create service-policy yherp-access Dial \
  --identity-roles '#employee' \
  --service-roles '#yherp'

# 创建边缘路由器策略
ziti edge create edge-router-policy yherp-router-access \
  --identity-roles '#employee' \
  --edge-router-roles '#internal'
```

#### 身份管理最佳实践
1. **最小权限原则**: 每个身份只授予必要的服务访问权限
2. **定期轮换**: 每90天轮换一次身份证书
3. **多因素认证**: 结合证书和额外认证因素
4. **审计日志**: 记录所有访问和操作日志

## 应用安全

### 1. SSL/TLS配置

#### 证书管理策略
```typescript
// 证书配置
ssl: {
  enableAutoManagement: true,
  validationMethod: 'DNS',
  certificateDomains: [
    '*.kh2u.com',      // 通配符证书
    'jmall.tw',        // 主域名
    '*.jmall.tw',      // 子域名
  ],
}
```

#### TLS安全配置
```bash
# ALB监听器TLS策略
Security Policy: ELBSecurityPolicy-TLS-1-2-2017-01

# 支持的TLS版本
TLS 1.2, TLS 1.3

# 支持的加密套件
ECDHE-ECDSA-AES128-GCM-SHA256
ECDHE-RSA-AES128-GCM-SHA256
ECDHE-ECDSA-AES256-GCM-SHA384
ECDHE-RSA-AES256-GCM-SHA384
```

### 2. 应用层安全

#### HTTP安全头配置
```typescript
// CloudFront响应头策略
securityHeadersBehavior: {
  contentTypeOptions: { override: true },
  frameOptions: { 
    frameOption: cloudfront.HeadersFrameOption.DENY, 
    override: true 
  },
  referrerPolicy: { 
    referrerPolicy: cloudfront.HeadersReferrerPolicy.STRICT_ORIGIN_WHEN_CROSS_ORIGIN, 
    override: true 
  },
  strictTransportSecurity: {
    accessControlMaxAge: cdk.Duration.seconds(31536000),
    includeSubdomains: true,
    preload: true,
    override: true,
  },
  xssProtection: { 
    protection: true, 
    modeBlock: true, 
    override: true 
  },
}
```

#### Odoo应用安全配置
```ini
[options]
# 数据库安全
db_password_file = /run/secrets/db_password
db_sslmode = require

# 会话安全
session_store = redis
session_redis_password_file = /run/secrets/redis_password

# 代理安全
proxy_mode = True
trusted_hosts = dp.kh2u.com,yh.kh2u.com,jmall.tw

# 安全头
secure_cookie_httponly = True
secure_cookie_secure = True
```

## 数据安全

### 1. 数据库安全

#### PostgreSQL安全配置
```sql
-- 启用SSL连接
ssl = on
ssl_cert_file = '/etc/ssl/certs/server.crt'
ssl_key_file = '/etc/ssl/private/server.key'

-- 连接安全
listen_addresses = 'localhost'
port = 5432
max_connections = 100

-- 认证配置
password_encryption = scram-sha-256
```

#### 数据加密
```bash
# 静态数据加密
EBS卷加密: 启用
RDS加密: 启用
S3存储桶加密: AES-256

# 传输中数据加密
数据库连接: SSL/TLS
Redis连接: TLS
应用通信: HTTPS
```

### 2. 密钥管理

#### AWS Secrets Manager配置
```typescript
// 数据库密钥
const databaseSecret = new secretsmanager.Secret(this, 'DatabaseSecret', {
  secretName: `yuanhui-db-credentials-${config.environment}`,
  generateSecretString: {
    secretStringTemplate: JSON.stringify({
      username: 'odoo_admin',
    }),
    generateStringKey: 'password',
    excludeCharacters: '"@/\\',
    passwordLength: 32,
  },
});

// 自动轮换配置
new secretsmanager.RotationSchedule(this, 'DatabaseSecretRotation', {
  secret: databaseSecret,
  rotationLambda: rotationLambda,
  automaticallyAfter: cdk.Duration.days(30),
});
```

#### 密钥轮换策略
1. **数据库密码**: 每30天自动轮换
2. **API密钥**: 每90天手动轮换
3. **SSL证书**: 自动续期（ACM管理）
4. **OpenZiti证书**: 每180天轮换

## 访问控制

### 1. IAM权限管理

#### 角色分离原则
```typescript
// ECS任务角色 - 最小权限
const taskRole = new iam.Role(this, 'EcsTaskRole', {
  assumedBy: new iam.ServicePrincipal('ecs-tasks.amazonaws.com'),
  inlinePolicies: {
    'SecretsManagerAccess': new iam.PolicyDocument({
      statements: [
        new iam.PolicyStatement({
          effect: iam.Effect.ALLOW,
          actions: ['secretsmanager:GetSecretValue'],
          resources: [databaseSecret.secretArn],
        }),
      ],
    }),
  },
});

// ECS执行角色 - 容器管理权限
const executionRole = new iam.Role(this, 'EcsExecutionRole', {
  assumedBy: new iam.ServicePrincipal('ecs-tasks.amazonaws.com'),
  managedPolicies: [
    iam.ManagedPolicy.fromAwsManagedPolicyName(
      'service-role/AmazonECSTaskExecutionRolePolicy'
    ),
  ],
});
```

#### 权限边界配置
```json
{
  "Version": "2012-10-17",
  "Statement": [
    {
      "Effect": "Allow",
      "Action": [
        "ec2:Describe*",
        "ecs:Describe*",
        "logs:CreateLogGroup",
        "logs:CreateLogStream",
        "logs:PutLogEvents"
      ],
      "Resource": "*"
    },
    {
      "Effect": "Deny",
      "Action": [
        "iam:*",
        "organizations:*",
        "account:*"
      ],
      "Resource": "*"
    }
  ]
}
```

### 2. 网络访问控制

#### 多层防护策略
```
Internet Gateway
    ↓
WAF (Web Application Firewall)
    ↓
Public ALB (公网负载均衡器)
    ↓
Security Groups (安全组)
    ↓
ECS Services (应用服务)
    ↓
Private Subnets (私有子网)
    ↓
Database Security Groups
    ↓
Database Subnets (数据库子网)
```

#### OpenZiti访问控制
```bash
# 服务定义
ziti edge create config yherp-intercept-config intercept.v1 \
  '{"protocols":["tcp"],"addresses":["yherp.internal"],"portRanges":[{"low":80,"high":80}]}'

# 主机配置
ziti edge create config yherp-host-config host.v1 \
  '{"protocol":"tcp","address":"internal-alb.amazonaws.com","port":80}'

# 访问策略
ziti edge create service-policy yherp-internal-access Dial \
  --identity-roles '#employee' \
  --service-roles '@yherp-internal'
```

## 监控和审计

### 1. 安全监控

#### CloudWatch告警配置
```typescript
// WAF阻止请求告警
new cloudwatch.Alarm(this, 'WafBlockedRequestsAlarm', {
  metric: new cloudwatch.Metric({
    namespace: 'AWS/WAFV2',
    metricName: 'BlockedRequests',
    dimensionsMap: {
      WebACL: webAcl.name,
    },
  }),
  threshold: 100,
  evaluationPeriods: 2,
  treatMissingData: cloudwatch.TreatMissingData.NOT_BREACHING,
});

// 异常登录告警
new cloudwatch.Alarm(this, 'UnusualLoginAlarm', {
  metric: new cloudwatch.Metric({
    namespace: 'AWS/ApplicationELB',
    metricName: 'HTTPCode_Target_4XX_Count',
    dimensionsMap: {
      LoadBalancer: loadBalancer.loadBalancerFullName,
    },
  }),
  threshold: 50,
  evaluationPeriods: 3,
});
```

#### 安全事件响应
1. **自动响应**: WAF自动阻止恶意请求
2. **告警通知**: SNS主题发送安全告警
3. **日志分析**: CloudWatch Insights分析安全日志
4. **事件调查**: CloudTrail记录API调用

### 2. 合规性审计

#### 日志记录策略
```typescript
// VPC Flow Logs
new ec2.FlowLog(this, 'VpcFlowLog', {
  resourceType: ec2.FlowLogResourceType.fromVpc(vpc),
  destination: ec2.FlowLogDestination.toCloudWatchLogs(flowLogGroup),
  trafficType: ec2.FlowLogTrafficType.ALL,
});

// ALB访问日志
loadBalancer.logAccessLogs(accessLogsBucket, 'alb-logs');

// WAF日志
new wafv2.CfnLoggingConfiguration(this, 'WafLogging', {
  resourceArn: webAcl.attrArn,
  logDestinationConfigs: [wafLogGroup.logGroupArn],
});
```

#### 定期安全检查清单
```bash
# 每日检查
□ 查看WAF阻止的请求
□ 检查异常登录活动
□ 验证SSL证书状态
□ 查看安全组变更

# 每周检查
□ 审查IAM权限变更
□ 检查未使用的资源
□ 验证备份完整性
□ 更新安全补丁

# 每月检查
□ 轮换访问密钥
□ 审查网络配置
□ 更新WAF规则
□ 安全漏洞扫描

# 每季度检查
□ 全面安全审计
□ 渗透测试
□ 灾难恢复演练
□ 合规性评估
```

## 事件响应

### 1. 安全事件分类

#### 高危事件
- 数据泄露
- 系统入侵
- 恶意软件感染
- DDoS攻击

#### 中危事件
- 异常登录
- 权限滥用
- 配置错误
- 服务中断

#### 低危事件
- 密码策略违反
- 访问日志异常
- 性能问题
- 配置漂移

### 2. 响应流程

#### 事件检测
1. **自动检测**: CloudWatch告警、WAF规则触发
2. **手动发现**: 日志分析、用户报告
3. **第三方通知**: 安全厂商、威胁情报

#### 响应步骤
1. **确认事件**: 验证告警真实性
2. **评估影响**: 确定影响范围和严重程度
3. **隔离威胁**: 阻止恶意流量、隔离受影响系统
4. **收集证据**: 保存日志、截图、网络流量
5. **修复漏洞**: 应用补丁、更新配置
6. **恢复服务**: 验证系统安全后恢复正常服务
7. **事后分析**: 总结经验、改进安全措施

## 持续改进

### 1. 安全评估

#### 定期评估项目
- 威胁建模更新
- 漏洞扫描
- 渗透测试
- 代码安全审查

#### 评估工具
- AWS Security Hub
- AWS Inspector
- AWS GuardDuty
- 第三方安全扫描工具

### 2. 安全培训

#### 培训内容
- 安全意识培训
- 技术安全培训
- 事件响应培训
- 合规性培训

#### 培训频率
- 新员工入职培训
- 年度安全培训
- 季度技术更新
- 事件后专项培训

---

**注意**: 本安全最佳实践文档应定期更新，以反映最新的威胁情况和安全技术发展。建议每季度审查一次，确保安全措施的有效性。
