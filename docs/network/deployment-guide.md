# 元晖网络基础设施部署指南

## 概述

本文档详细说明了元晖服务器网络入口和路由配置的完整部署流程，包括多域名路由、SSL证书管理、WAF防护、CloudFront CDN和OpenZiti零信任网络的配置。

## 架构概览

### 域名和访问控制配置

| 域名 | 目标应用 | 访问控制 | 负载均衡器 | 特殊配置 |
|------|----------|----------|------------|----------|
| yh.kh2u.com | yherp | 内部访问 | 内部ALB | OpenZiti零信任网络 + Longpolling |
| dp.kh2u.com | yherp | 公网访问 | 公网ALB | WAF防护 + Longpolling |
| jmall.tw | khmall | 公网访问 | 公网ALB | CloudFront CDN加速 + Longpolling |

### 技术栈组件

- **DNS管理**: AWS Route53
- **SSL证书**: AWS Certificate Manager (ACM)
- **负载均衡**: Application Load Balancer (ALB)
- **WAF防护**: AWS WAF v2
- **CDN加速**: Amazon CloudFront
- **零信任网络**: OpenZiti
- **容器编排**: Amazon ECS
- **基础设施即代码**: AWS CDK (TypeScript)

### Odoo Longpolling配置

根据Odoo 18官方文档，本项目实现了完整的longpolling支持：

- **端口配置**: 主应用端口8069 + Longpolling端口8072
- **路径路由**: `/longpolling/*` 和 `/websocket/*` 自动路由到8072端口
- **实时功能**: 支持聊天、通知、实时更新等功能
- **会话管理**: 主应用启用会话粘性，longpolling无需粘性
- **健康检查**: 分别配置8069和8072端口的健康检查

## 前置条件

### 1. 环境要求

```bash
# 必需软件
- Node.js >= 18.0.0
- npm >= 8.0.0
- AWS CLI >= 2.0.0
- AWS CDK >= 2.0.0

# 可选工具
- jq (用于JSON处理)
- curl (用于测试)
- openssl (用于SSL验证)
```

### 2. AWS权限要求

部署账户需要以下AWS服务的完整权限：
- CloudFormation
- EC2 (VPC, 安全组, 负载均衡器)
- ECS (集群, 服务, 任务定义)
- Route53 (托管区域, DNS记录)
- Certificate Manager (证书管理)
- WAF v2 (Web ACL, 规则)
- CloudFront (分发, 缓存策略)
- CloudWatch (日志, 监控)
- IAM (角色, 策略)

### 3. 域名准备

确保您拥有以下域名的管理权限：
- `kh2u.com` (主域名)
- `jmall.tw` (电商域名)

## 部署流程

### 第一步：环境配置

1. **克隆项目并安装依赖**
```bash
git clone <repository-url>
cd iac
npm install
```

2. **配置AWS凭证**
```bash
aws configure
# 输入Access Key ID、Secret Access Key、Region等信息
```

3. **设置环境变量**
```bash
# 开发环境
export NODE_ENV=dev

# 生产环境
export NODE_ENV=prod
```

### 第二步：配置域名和证书

1. **更新环境配置**

编辑 `lib/config/environment.ts`，根据实际情况调整域名配置：

```typescript
domains: {
  primary: {
    zoneName: 'kh2u.com',
    // hostedZoneId: 'Z1234567890ABC', // 如果已存在托管区域
  },
  applications: {
    yherp: {
      internal: 'yh.kh2u.com',    // 内部访问域名
      public: 'dp.kh2u.com',      // 公网访问域名
    },
    khmall: {
      domain: 'jmall.tw',         // 电商平台域名
    },
  },
},
```

2. **配置SSL证书域名**
```typescript
ssl: {
  enableAutoManagement: true,
  validationMethod: 'DNS',
  certificateDomains: [
    '*.kh2u.com',
    'jmall.tw',
    '*.jmall.tw',  // 生产环境
  ],
},
```

### 第三步：配置安全策略

1. **WAF规则配置**
```typescript
waf: {
  enabled: true,
  rules: {
    enableAWSManagedRules: true,
    enableIPWhitelist: true,
    whitelistIPs: [
      '***********/24',  // 企业办公网络
      '************/24', // VPN网络
    ],
    enableGeoBlocking: true,
    blockedCountries: ['CN', 'RU', 'KP'],
    enableRateLimit: true,
    rateLimit: {
      limit: 2000,
      windowSize: 300,
    },
  },
},
```

2. **OpenZiti配置**
```typescript
openziti: {
  enabled: true,  // 生产环境启用
  controller: {
    endpoint: 'ziti-controller.kh2u.com',
    port: 443,
  },
  network: {
    name: 'yuanhui-prod-network',
    cidr: '**********/16',
  },
},
```

### 第四步：执行部署

1. **使用自动化部署脚本**
```bash
# 开发环境部署
./scripts/deploy-network-infrastructure.sh dev

# 生产环境部署
./scripts/deploy-network-infrastructure.sh prod

# 部署所有栈（快速模式）
./scripts/deploy-network-infrastructure.sh prod true
```

2. **手动分步部署**
```bash
# 1. 编译代码
npm run build

# 2. CDK Bootstrap
cdk bootstrap

# 3. 按顺序部署栈
cdk deploy YuanhuiNetwork-prod --require-approval never
cdk deploy YuanhuiEcs-prod --require-approval never
cdk deploy YuanhuiDatabase-prod --require-approval never
cdk deploy YuanhuiDns-prod --require-approval never
cdk deploy YuanhuiApplication-prod --require-approval never
cdk deploy YuanhuiCloudFront-prod --require-approval never
cdk deploy YuanhuiOpenZiti-prod --require-approval never
cdk deploy YuanhuiMonitoring-prod --require-approval never
```

### 第五步：DNS配置

1. **获取域名服务器**
```bash
aws cloudformation describe-stacks \
  --stack-name YuanhuiDns-prod \
  --query 'Stacks[0].Outputs[?OutputKey==`NameServers`].OutputValue' \
  --output text
```

2. **配置域名解析**
- 登录域名注册商管理界面
- 将域名的NS记录指向AWS Route53的域名服务器
- 等待DNS传播完成（24-48小时）

### 第六步：验证部署

1. **运行自动化测试**
```bash
# 网络路由测试
./scripts/test-network-routing.sh prod

# SSL证书验证
./scripts/verify-ssl-certificates.sh prod
```

2. **手动验证**
```bash
# 检查域名解析
nslookup dp.kh2u.com
nslookup jmall.tw

# 测试HTTPS连接
curl -I https://dp.kh2u.com
curl -I https://jmall.tw

# 检查SSL证书
openssl s_client -servername dp.kh2u.com -connect dp.kh2u.com:443
```

## 部署后配置

### 1. OpenZiti客户端设置

对于需要访问 `yh.kh2u.com` 的内部用户：

1. **安装OpenZiti客户端**
```bash
curl -sSLf https://get.openziti.io/install.sh | bash
```

2. **获取身份配置文件**
- 联系系统管理员获取身份配置文件
- 使用 `ziti edge enroll` 命令注册身份

3. **连接到网络**
```bash
ziti-edge-tunnel run-host <identity-file>
```

### 2. 应用配置

1. **Odoo配置文件更新**
```ini
[options]
# 数据库配置
db_host = <pgpool-endpoint>
db_port = 5434
db_user = odoo_admin
db_password = <database-password>

# 会话配置
session_store = redis
session_redis_host = <redis-endpoint>
session_redis_port = 6379

# 代理配置
proxy_mode = True
```

2. **反向代理头部配置**
ALB已自动配置以下头部：
- `X-Forwarded-For`
- `X-Forwarded-Proto`
- `X-Forwarded-Host`
- `X-Real-IP`

### 3. 监控配置

1. **CloudWatch仪表板**
访问：https://console.aws.amazon.com/cloudwatch/

2. **告警配置**
```bash
# 订阅SNS主题接收告警
aws sns subscribe \
  --topic-arn <alarm-topic-arn> \
  --protocol email \
  --notification-endpoint <EMAIL>
```

3. **日志查看**
```bash
# 应用日志
aws logs tail /aws/ecs/yherp-prod --follow
aws logs tail /aws/ecs/khmall-prod --follow

# WAF日志
aws logs tail /aws/wafv2/yuanhui-prod --follow
```

## 故障排除

### 常见问题

1. **SSL证书验证失败**
```bash
# 检查DNS验证记录
./scripts/verify-ssl-certificates.sh prod

# 手动添加DNS验证记录
aws route53 change-resource-record-sets --hosted-zone-id <zone-id> --change-batch file://dns-validation.json
```

2. **域名解析问题**
```bash
# 检查NS记录配置
dig NS kh2u.com
dig NS jmall.tw

# 清除DNS缓存
sudo dscacheutil -flushcache  # macOS
sudo systemctl restart systemd-resolved  # Ubuntu
```

3. **负载均衡器健康检查失败**
```bash
# 检查目标组状态
aws elbv2 describe-target-health --target-group-arn <target-group-arn>

# 检查安全组规则
aws ec2 describe-security-groups --group-ids <security-group-id>
```

4. **OpenZiti连接问题**
```bash
# 检查控制器状态
curl -k https://ziti-controller.kh2u.com:443/health-checks

# 查看服务日志
aws logs tail /aws/ecs/ziti-controller-prod --follow
```

### 回滚策略

如果部署出现问题，可以使用以下命令回滚：

```bash
# 回滚特定栈
cdk deploy YuanhuiApplication-prod --rollback

# 删除有问题的栈并重新部署
cdk destroy YuanhuiApplication-prod
cdk deploy YuanhuiApplication-prod
```

## 性能优化建议

1. **CloudFront缓存优化**
- 静态资源缓存时间：1年
- 动态内容缓存时间：1小时
- 启用Gzip压缩

2. **数据库连接优化**
- 使用PgPool连接池
- 配置读写分离
- 启用连接复用

3. **ECS服务优化**
- 配置自动扩缩容
- 使用Spot实例降低成本
- 启用容器洞察

## 安全最佳实践

1. **定期更新**
- 每月更新容器镜像
- 定期轮换数据库密码
- 更新WAF规则

2. **访问控制**
- 使用IAM角色而非用户密钥
- 启用MFA
- 定期审查权限

3. **监控和审计**
- 启用CloudTrail
- 配置安全告警
- 定期安全扫描

## 成本优化

1. **资源优化**
- 使用预留实例
- 配置自动扩缩容
- 定期清理未使用资源

2. **监控成本**
- 设置预算告警
- 使用Cost Explorer分析
- 优化数据传输成本

---

**注意**: 本部署指南基于AWS CDK和最佳实践编写，请根据实际环境调整配置参数。如有问题，请参考AWS官方文档或联系技术支持。
