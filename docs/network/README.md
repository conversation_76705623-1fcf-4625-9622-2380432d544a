# 元晖网络基础设施配置

## 项目概述

本项目实现了元晖服务器的完整网络入口和路由配置，基于AWS CDK构建，支持多域名路由、SSL证书自动管理、WAF防护、CloudFront CDN加速和OpenZiti零信任网络访问控制。

## 架构特性

### 🌐 多域名路由配置

| 域名 | 应用 | 访问方式 | 负载均衡器 | 特殊功能 |
|------|------|----------|------------|----------|
| **yh.kh2u.com** | Yherp | 内部访问 | 内部ALB | OpenZiti零信任网络 + Longpolling |
| **dp.kh2u.com** | Yherp | 公网访问 | 公网ALB | WAF防护 + Longpolling |
| **jmall.tw** | Khmall | 电商平台 | 公网ALB | CloudFront CDN加速 + Longpolling |

### 🔒 安全防护体系

- **多层WAF防护**: AWS托管规则 + 自定义规则
- **SSL/TLS加密**: 自动证书管理和续期
- **零信任网络**: OpenZiti内部访问控制
- **网络隔离**: VPC子网分层设计
- **访问控制**: 基于角色的权限管理

### ⚡ 性能优化

- **全球CDN**: CloudFront边缘节点加速
- **会话粘性**: 支持Odoo多数据库架构
- **连接池**: PgPool数据库连接优化
- **自动扩缩容**: 基于CPU/内存的弹性伸缩

## 快速开始

### 1. 环境准备

```bash
# 安装依赖
npm install

# 配置AWS凭证
aws configure

# 设置环境变量
export NODE_ENV=prod  # 或 dev
```

### 2. 配置域名

编辑 `lib/config/environment.ts`：

```typescript
domains: {
  primary: {
    zoneName: 'kh2u.com',
  },
  applications: {
    yherp: {
      internal: 'yh.kh2u.com',
      public: 'dp.kh2u.com',
    },
    khmall: {
      domain: 'jmall.tw',
    },
  },
},
```

### 3. 一键部署

```bash
# 自动化部署
./scripts/deploy-network-infrastructure.sh prod

# 或手动部署
npm run build
cdk bootstrap
cdk deploy --all
```

### 4. 验证部署

```bash
# 运行测试套件
./scripts/test-network-routing.sh prod
./scripts/verify-ssl-certificates.sh prod
```

## 技术栈

### 基础设施即代码
- **AWS CDK**: TypeScript实现
- **CloudFormation**: 底层资源管理
- **Git**: 版本控制和CI/CD

### 网络组件
- **Route53**: DNS管理和健康检查
- **Certificate Manager**: SSL证书自动管理
- **Application Load Balancer**: 7层负载均衡
- **WAF v2**: Web应用防火墙
- **CloudFront**: 全球内容分发网络

### 安全组件
- **OpenZiti**: 零信任网络架构
- **VPC**: 网络隔离和安全组
- **Secrets Manager**: 密钥管理和轮换
- **CloudTrail**: API调用审计

### 监控组件
- **CloudWatch**: 指标监控和告警
- **VPC Flow Logs**: 网络流量分析
- **ALB访问日志**: 请求分析
- **WAF日志**: 安全事件记录

## 文档结构

```
docs/network/
├── README.md                    # 项目概述（本文件）
├── deployment-guide.md          # 详细部署指南
├── security-best-practices.md   # 安全配置最佳实践
└── troubleshooting-guide.md     # 故障排除指南

scripts/
├── deploy-network-infrastructure.sh  # 自动化部署脚本
├── test-network-routing.sh          # 网络路由测试
└── verify-ssl-certificates.sh       # SSL证书验证

lib/stacks/
├── network-stack.ts            # 网络和安全组配置
├── dns-stack.ts               # DNS和SSL证书管理
├── application-stack.ts       # 负载均衡器和路由
├── cloudfront-stack.ts        # CDN配置
└── openziti-stack.ts          # 零信任网络配置
```

## 配置说明

### 环境配置

项目支持多环境部署，主要配置文件：`lib/config/environment.ts`

#### 开发环境 (dev)
- 单可用区部署
- 基础WAF规则
- 不启用CloudFront
- 简化的OpenZiti配置

#### 生产环境 (prod)
- 多可用区高可用
- 完整WAF规则集
- CloudFront CDN加速
- 完整OpenZiti零信任网络

### 域名配置

```typescript
// 主域名配置
primary: {
  zoneName: 'kh2u.com',
  hostedZoneId: 'Z1234567890ABC', // 可选，如果已存在
},

// 应用域名配置
applications: {
  yherp: {
    internal: 'yh.kh2u.com',    // 内部访问
    public: 'dp.kh2u.com',      // 公网访问
  },
  khmall: {
    domain: 'jmall.tw',         // 电商平台
  },
},
```

### 安全配置

```typescript
// WAF配置
waf: {
  enabled: true,
  rules: {
    enableAWSManagedRules: true,
    enableIPWhitelist: true,
    whitelistIPs: ['***********/24'],
    enableGeoBlocking: true,
    blockedCountries: ['CN', 'RU', 'KP'],
    enableRateLimit: true,
    rateLimit: { limit: 2000, windowSize: 300 },
  },
},

// OpenZiti配置
openziti: {
  enabled: true,
  controller: {
    endpoint: 'ziti-controller.kh2u.com',
    port: 443,
  },
  network: {
    name: 'yuanhui-prod-network',
    cidr: '**********/16',
  },
},
```

## 运维指南

### 日常监控

```bash
# 检查系统状态
aws cloudformation describe-stacks --stack-name YuanhuiApplication-prod

# 查看应用日志
aws logs tail /aws/ecs/yherp-prod --follow

# 检查WAF防护
aws logs filter-log-events \
  --log-group-name "/aws/wafv2/yuanhui-prod" \
  --filter-pattern "BLOCK"

# 验证SSL证书
./scripts/verify-ssl-certificates.sh prod
```

### 性能优化

1. **CloudFront缓存优化**
   - 静态资源缓存：1年
   - 动态内容缓存：1小时
   - 启用Gzip压缩

2. **数据库连接优化**
   - PgPool连接池
   - 读写分离配置
   - 连接复用

3. **ECS服务优化**
   - 自动扩缩容配置
   - 健康检查优化
   - 资源限制调优

### 安全维护

1. **定期更新**
   - 每月更新容器镜像
   - 每季度更新WAF规则
   - 每年进行安全审计

2. **密钥管理**
   - 数据库密码：30天轮换
   - API密钥：90天轮换
   - SSL证书：自动续期

3. **访问控制**
   - 定期审查IAM权限
   - 更新OpenZiti身份
   - 监控异常访问

## 故障排除

### 常见问题

1. **域名解析失败**
   - 检查NS记录配置
   - 验证Route53托管区域
   - 清除DNS缓存

2. **SSL证书问题**
   - 验证DNS验证记录
   - 检查证书有效期
   - 重新申请证书

3. **负载均衡器问题**
   - 检查目标组健康状态
   - 验证安全组规则
   - 测试健康检查端点

4. **WAF误报**
   - 分析WAF日志
   - 调整规则优先级
   - 添加白名单例外

详细故障排除步骤请参考：[故障排除指南](troubleshooting-guide.md)

## 成本优化

### 资源优化建议

1. **计算资源**
   - 使用Spot实例降低成本
   - 配置自动扩缩容
   - 优化实例规格

2. **存储优化**
   - 使用生命周期策略
   - 压缩日志文件
   - 定期清理未使用资源

3. **网络优化**
   - 优化数据传输
   - 使用CloudFront减少源站流量
   - 配置VPC端点

### 成本监控

```bash
# 设置预算告警
aws budgets create-budget \
  --account-id <account-id> \
  --budget file://budget-config.json

# 查看成本分析
aws ce get-cost-and-usage \
  --time-period Start=2024-01-01,End=2024-01-31 \
  --granularity MONTHLY \
  --metrics BlendedCost
```

## 贡献指南

### 开发流程

1. **创建功能分支**
   ```bash
   git checkout -b feature/new-network-feature
   ```

2. **开发和测试**
   ```bash
   npm run build
   npm run test
   cdk diff
   ```

3. **提交变更**
   ```bash
   git add .
   git commit -m "feat: add new network feature"
   git push origin feature/new-network-feature
   ```

4. **创建Pull Request**
   - 描述变更内容
   - 包含测试结果
   - 更新相关文档

### 代码规范

- 使用TypeScript严格模式
- 遵循AWS CDK最佳实践
- 添加适当的注释和文档
- 包含单元测试和集成测试

## 支持和联系

### 技术支持

- **文档**: [部署指南](deployment-guide.md) | [安全最佳实践](security-best-practices.md)
- **问题反馈**: 创建GitHub Issue
- **紧急支持**: <EMAIL>

### 相关资源

- [AWS CDK文档](https://docs.aws.amazon.com/cdk/)
- [Odoo官方文档](https://www.odoo.com/documentation/)
- [OpenZiti文档](https://docs.openziti.io/)
- [AWS安全最佳实践](https://aws.amazon.com/security/security-resources/)

---

**版本**: v1.0.0  
**最后更新**: 2024年12月  
**维护者**: 元晖技术团队
