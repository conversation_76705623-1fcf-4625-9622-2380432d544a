# 网络故障排除指南

## 概述

本文档提供了元晖网络基础设施常见问题的诊断和解决方案，包括域名解析、SSL证书、负载均衡器、WAF防护等各个组件的故障排除方法。

## 域名和DNS问题

### 问题1：域名无法解析

**症状**
- 用户无法访问 yh.kh2u.com、dp.kh2u.com 或 jmall.tw
- DNS查询返回 NXDOMAIN 错误

**诊断步骤**
```bash
# 1. 检查域名解析
nslookup dp.kh2u.com
dig dp.kh2u.com

# 2. 检查NS记录
dig NS kh2u.com
dig NS jmall.tw

# 3. 检查Route53托管区域
aws route53 list-hosted-zones
aws route53 list-resource-record-sets --hosted-zone-id Z1234567890ABC
```

**解决方案**
1. **检查域名注册商NS记录**
   - 确保NS记录指向AWS Route53的域名服务器
   - 等待DNS传播完成（最多48小时）

2. **验证Route53配置**
   ```bash
   # 检查A记录是否正确指向ALB
   aws route53 list-resource-record-sets \
     --hosted-zone-id Z1234567890ABC \
     --query 'ResourceRecordSets[?Type==`A`]'
   ```

3. **重新创建DNS记录**
   ```bash
   # 如果记录缺失，重新部署DNS栈
   cdk deploy YuanhuiDns-prod
   ```

### 问题2：DNS解析缓慢

**症状**
- 域名解析时间超过5秒
- 间歇性解析失败

**诊断步骤**
```bash
# 检查DNS响应时间
time nslookup dp.kh2u.com
dig +trace dp.kh2u.com

# 检查不同DNS服务器的响应
dig @******* dp.kh2u.com
dig @******* dp.kh2u.com
```

**解决方案**
1. **优化TTL设置**
   - 调整DNS记录的TTL值
   - 平衡缓存效果和更新速度

2. **检查Route53健康检查**
   ```bash
   aws route53 list-health-checks
   aws route53 get-health-check --health-check-id <health-check-id>
   ```

## SSL证书问题

### 问题3：SSL证书验证失败

**症状**
- 浏览器显示"证书无效"警告
- HTTPS连接失败

**诊断步骤**
```bash
# 1. 检查证书状态
./scripts/verify-ssl-certificates.sh prod

# 2. 手动检查证书
openssl s_client -servername dp.kh2u.com -connect dp.kh2u.com:443

# 3. 检查ACM证书状态
aws acm list-certificates
aws acm describe-certificate --certificate-arn <cert-arn>
```

**解决方案**
1. **重新验证证书**
   ```bash
   # 检查DNS验证记录
   aws acm describe-certificate --certificate-arn <cert-arn> \
     --query 'Certificate.DomainValidationOptions'
   
   # 添加缺失的DNS验证记录
   aws route53 change-resource-record-sets \
     --hosted-zone-id <zone-id> \
     --change-batch file://dns-validation.json
   ```

2. **重新申请证书**
   ```bash
   # 删除旧证书并重新部署
   cdk destroy YuanhuiDns-prod
   cdk deploy YuanhuiDns-prod
   ```

### 问题4：证书即将过期

**症状**
- 证书有效期少于30天
- 自动续期失败

**诊断步骤**
```bash
# 检查证书过期时间
aws acm describe-certificate --certificate-arn <cert-arn> \
  --query 'Certificate.NotAfter'

# 检查证书续期历史
aws logs filter-log-events \
  --log-group-name "/aws/acm/certificate-manager" \
  --filter-pattern "renewal"
```

**解决方案**
1. **手动触发续期**
   - ACM会自动续期，但需要DNS验证记录存在
   - 确保Route53中的验证记录未被删除

2. **监控证书状态**
   ```bash
   # 设置CloudWatch告警
   aws cloudwatch put-metric-alarm \
     --alarm-name "SSL-Certificate-Expiry" \
     --alarm-description "SSL certificate expiring soon" \
     --metric-name DaysToExpiry \
     --namespace AWS/CertificateManager \
     --statistic Minimum \
     --period 86400 \
     --threshold 30 \
     --comparison-operator LessThanThreshold
   ```

## 负载均衡器问题

### 问题5：负载均衡器健康检查失败

**症状**
- 目标组显示"unhealthy"状态
- 502/503错误频繁出现

**诊断步骤**
```bash
# 1. 检查目标组健康状态
aws elbv2 describe-target-health \
  --target-group-arn <target-group-arn>

# 2. 检查ECS服务状态
aws ecs describe-services \
  --cluster yuanhui-odoo-prod \
  --services yherp-prod khmall-prod

# 3. 检查安全组规则
aws ec2 describe-security-groups \
  --group-ids <alb-security-group-id> <ecs-security-group-id>
```

**解决方案**
1. **修复安全组规则**
   ```bash
   # 确保ALB可以访问ECS服务
   aws ec2 authorize-security-group-ingress \
     --group-id <ecs-security-group-id> \
     --protocol tcp \
     --port 8069 \
     --source-group <alb-security-group-id>
   ```

2. **检查健康检查路径**
   ```bash
   # 测试健康检查端点
   curl -I http://<ecs-instance-ip>:8069/web/health
   
   # 如果路径不存在，更新目标组配置
   aws elbv2 modify-target-group \
     --target-group-arn <target-group-arn> \
     --health-check-path "/web/database/selector"
   ```

3. **重启ECS服务**
   ```bash
   aws ecs update-service \
     --cluster yuanhui-odoo-prod \
     --service yherp-prod \
     --force-new-deployment
   ```

### 问题6：Host header路由不工作

**症状**
- 访问特定域名返回404错误
- 路由规则未正确匹配

**诊断步骤**
```bash
# 1. 检查ALB监听器规则
aws elbv2 describe-rules \
  --listener-arn <listener-arn>

# 2. 测试Host header路由
curl -H "Host: dp.kh2u.com" http://<alb-dns-name>/
curl -H "Host: jmall.tw" http://<alb-dns-name>/

# 3. 检查ALB访问日志
aws s3 ls s3://<alb-logs-bucket>/AWSLogs/<account-id>/elasticloadbalancing/
```

**解决方案**
1. **验证监听器规则优先级**
   ```bash
   # 检查规则优先级是否正确
   aws elbv2 describe-rules --listener-arn <listener-arn> \
     --query 'Rules[].{Priority:Priority,Conditions:Conditions}'
   ```

2. **更新路由规则**
   ```bash
   # 重新部署应用栈以修复路由规则
   cdk deploy YuanhuiApplication-prod
   ```

## WAF防护问题

### 问题7：合法请求被WAF阻止

**症状**
- 用户报告无法访问网站
- 返回403 Forbidden错误

**诊断步骤**
```bash
# 1. 检查WAF日志
aws logs filter-log-events \
  --log-group-name "/aws/wafv2/yuanhui-prod" \
  --filter-pattern "BLOCK" \
  --start-time $(date -d "1 hour ago" +%s)000

# 2. 分析被阻止的请求
aws logs filter-log-events \
  --log-group-name "/aws/wafv2/yuanhui-prod" \
  --filter-pattern "{ $.action = \"BLOCK\" }" \
  --start-time $(date -d "1 day ago" +%s)000
```

**解决方案**
1. **添加IP白名单**
   ```bash
   # 将合法IP添加到白名单
   aws wafv2 update-ip-set \
     --scope REGIONAL \
     --id <ip-set-id> \
     --addresses "*************/32"
   ```

2. **调整WAF规则**
   ```bash
   # 临时禁用特定规则
   aws wafv2 update-web-acl \
     --scope REGIONAL \
     --id <web-acl-id> \
     --rules file://updated-rules.json
   ```

3. **创建例外规则**
   ```typescript
   // 在CDK中添加例外规则
   {
     name: 'AllowSpecificPath',
     priority: 5,
     action: { allow: {} },
     statement: {
       byteMatchStatement: {
         searchString: '/api/webhook',
         fieldToMatch: { uriPath: {} },
         textTransformations: [{ priority: 0, type: 'LOWERCASE' }],
         positionalConstraint: 'STARTS_WITH'
       }
     }
   }
   ```

### 问题8：WAF规则过于宽松

**症状**
- 恶意请求未被阻止
- 安全日志显示可疑活动

**诊断步骤**
```bash
# 1. 分析WAF允许的请求
aws logs filter-log-events \
  --log-group-name "/aws/wafv2/yuanhui-prod" \
  --filter-pattern "ALLOW" \
  --start-time $(date -d "1 day ago" +%s)000

# 2. 检查攻击模式
aws logs filter-log-events \
  --log-group-name "/aws/wafv2/yuanhui-prod" \
  --filter-pattern "{ $.httpRequest.uri = \"*<script>*\" }" \
  --start-time $(date -d "1 week ago" +%s)000
```

**解决方案**
1. **启用更严格的规则**
   ```bash
   # 启用AWS托管规则集
   cdk deploy YuanhuiNetwork-prod
   ```

2. **降低速率限制阈值**
   ```typescript
   // 调整速率限制
   rateLimit: {
     limit: 1000,  // 从2000降低到1000
     windowSize: 300,
   }
   ```

## CloudFront问题

### 问题9：CloudFront缓存问题

**症状**
- 静态资源更新不及时
- 缓存命中率过低

**诊断步骤**
```bash
# 1. 检查CloudFront分发状态
aws cloudfront get-distribution --id <distribution-id>

# 2. 检查缓存统计
aws cloudwatch get-metric-statistics \
  --namespace AWS/CloudFront \
  --metric-name CacheHitRate \
  --dimensions Name=DistributionId,Value=<distribution-id> \
  --start-time $(date -d "1 day ago" -u +%Y-%m-%dT%H:%M:%S) \
  --end-time $(date -u +%Y-%m-%dT%H:%M:%S) \
  --period 3600 \
  --statistics Average
```

**解决方案**
1. **清除缓存**
   ```bash
   # 创建失效请求
   aws cloudfront create-invalidation \
     --distribution-id <distribution-id> \
     --paths "/*"
   ```

2. **优化缓存策略**
   ```typescript
   // 调整缓存行为
   cacheBehaviors: {
     defaultTTL: 3600,    // 1小时
     maxTTL: 86400,       // 1天
     minTTL: 0,
   }
   ```

## Odoo Longpolling问题

### 问题10：Longpolling功能不工作

**症状**
- 聊天功能无法实时更新
- 通知不能及时推送
- 多用户协作时数据不同步

**诊断步骤**
```bash
# 1. 检查longpolling端点
curl -I https://dp.kh2u.com/longpolling/poll

# 2. 检查目标组健康状态
aws elbv2 describe-target-health \
  --target-group-arn <longpolling-target-group-arn>

# 3. 检查ECS任务端口映射
aws ecs describe-tasks \
  --cluster yuanhui-odoo-prod \
  --tasks <task-arn>

# 4. 查看应用日志
aws logs filter-log-events \
  --log-group-name "/aws/ecs/yherp-prod" \
  --filter-pattern "longpolling"
```

**解决方案**
1. **检查端口配置**
   ```bash
   # 验证8072端口是否正确映射
   docker exec -it <container-id> netstat -tlnp | grep 8072
   ```

2. **验证路由规则**
   ```bash
   # 检查ALB监听器规则
   aws elbv2 describe-rules --listener-arn <listener-arn> \
     --query 'Rules[?Conditions[?Field==`path-pattern`]]'
   ```

3. **重启ECS服务**
   ```bash
   aws ecs update-service \
     --cluster yuanhui-odoo-prod \
     --service yherp-prod \
     --force-new-deployment
   ```

### 问题11：Longpolling连接超时

**症状**
- 浏览器控制台显示longpolling连接错误
- 实时功能间歇性失效

**诊断步骤**
```bash
# 1. 检查连接超时设置
aws elbv2 describe-target-groups \
  --target-group-arns <longpolling-target-group-arn> \
  --query 'TargetGroups[0].HealthCheckTimeoutSeconds'

# 2. 监控连接数
netstat -an | grep :8072 | wc -l

# 3. 检查ECS任务资源使用
aws ecs describe-services \
  --cluster yuanhui-odoo-prod \
  --services yherp-prod
```

**解决方案**
1. **调整健康检查配置**
   ```typescript
   healthCheck: {
     enabled: true,
     healthyHttpCodes: '200,404',
     path: '/longpolling/health',
     interval: cdk.Duration.seconds(30),
     timeout: cdk.Duration.seconds(10), // 增加超时时间
     healthyThresholdCount: 2,
     unhealthyThresholdCount: 5, // 增加容错次数
   }
   ```

2. **优化Odoo配置**
   ```ini
   # 增加worker数量
   workers = 4
   max_cron_threads = 2

   # 调整内存限制
   limit_memory_hard = **********
   limit_memory_soft = **********
   ```

## OpenZiti问题

### 问题12：OpenZiti连接失败

**症状**
- 内部用户无法访问 yh.kh2u.com
- Ziti客户端连接超时

**诊断步骤**
```bash
# 1. 检查Ziti控制器状态
curl -k https://ziti-controller.kh2u.com:443/health-checks

# 2. 检查ECS服务状态
aws ecs describe-services \
  --cluster yuanhui-odoo-prod \
  --services ziti-controller-prod ziti-router-prod

# 3. 查看Ziti日志
aws logs tail /aws/ecs/ziti-controller-prod --follow
```

**解决方案**
1. **重启Ziti服务**
   ```bash
   aws ecs update-service \
     --cluster yuanhui-odoo-prod \
     --service ziti-controller-prod \
     --force-new-deployment
   ```

2. **检查网络配置**
   ```bash
   # 验证安全组规则
   aws ec2 describe-security-groups \
     --group-ids <ziti-security-group-id>
   ```

3. **重新注册身份**
   ```bash
   # 客户端重新注册
   ziti edge delete identity <identity-name>
   ziti edge create identity user <identity-name>
   ziti edge enroll <enrollment-token>
   ```

## 应急响应流程

### 紧急情况处理

1. **服务完全不可用**
   ```bash
   # 1. 检查所有组件状态
   ./scripts/test-network-routing.sh prod
   
   # 2. 回滚到上一个稳定版本
   cdk deploy --rollback
   
   # 3. 启用维护页面
   aws s3 cp maintenance.html s3://<static-site-bucket>/index.html
   ```

2. **安全事件响应**
   ```bash
   # 1. 立即阻止可疑IP
   aws wafv2 update-ip-set \
     --scope REGIONAL \
     --id <blocked-ip-set-id> \
     --addresses "<malicious-ip>/32"
   
   # 2. 启用更严格的WAF规则
   aws wafv2 update-web-acl \
     --scope REGIONAL \
     --id <web-acl-id> \
     --default-action Block={}
   
   # 3. 收集证据
   aws logs create-export-task \
     --log-group-name "/aws/wafv2/yuanhui-prod" \
     --from $(date -d "1 hour ago" +%s)000 \
     --to $(date +%s)000 \
     --destination s3://<security-logs-bucket>/incident-$(date +%Y%m%d-%H%M%S)
   ```

### 联系信息

- **技术支持**: <EMAIL>
- **安全事件**: <EMAIL>
- **紧急联系**: +86-xxx-xxxx-xxxx

---

**注意**: 本故障排除指南应定期更新，以反映基础设施的变化和新发现的问题。建议每月审查一次，确保所有命令和流程的准确性。
