# Odoo Longpolling配置指南

## 概述

本文档详细说明了Odoo longpolling功能的配置和实现，基于Odoo 18官方文档的最佳实践，确保实时通信功能（如聊天、通知、实时更新）正常工作。

## Longpolling技术背景

### 什么是Longpolling

Longpolling是一种Web实时通信技术，允许服务器主动向客户端推送数据。在Odoo中，longpolling用于：

- **实时聊天**: 即时消息和在线状态
- **通知系统**: 实时推送系统通知
- **界面更新**: 动态更新用户界面
- **协作功能**: 多用户同时编辑时的实时同步

### Odoo Longpolling架构

根据Odoo官方文档，longpolling需要专门的端口和配置：

- **主应用端口**: 8069（处理常规HTTP请求）
- **Longpolling端口**: 8072（处理长连接请求）
- **路径识别**: `/longpolling/*` 和 `/websocket/*` 路径

## 网络配置实现

### 1. 安全组配置

在`network-stack.ts`中，我们为ECS安全组添加了8072端口的访问规则：

```typescript
// 允许来自公网ALB的longpolling流量
this.ecsSecurityGroup.addIngressRule(
  this.albSecurityGroup,
  ec2.Port.tcp(8072),
  'Allow longpolling traffic from public ALB to Odoo'
);

// 允许来自内部ALB的longpolling流量
this.ecsSecurityGroup.addIngressRule(
  this.internalAlbSecurityGroup,
  ec2.Port.tcp(8072),
  'Allow longpolling traffic from internal ALB to Odoo'
);
```

### 2. 负载均衡器目标组

创建专门的longpolling目标组：

```typescript
const yherpLongpollingTargetGroup = new elbv2.ApplicationTargetGroup(this, 'YherpLongpollingTargetGroup', {
  vpc,
  port: 8072,
  protocol: elbv2.ApplicationProtocol.HTTP,
  targetType: elbv2.TargetType.INSTANCE,
  targetGroupName: `yherp-longpoll-tg-${config.environment}`,
  healthCheck: {
    enabled: true,
    healthyHttpCodes: '200,404', // longpolling端点可能返回404
    path: '/longpolling/health',
    interval: cdk.Duration.seconds(30),
    timeout: cdk.Duration.seconds(5),
    healthyThresholdCount: 2,
    unhealthyThresholdCount: 3,
    protocol: elbv2.Protocol.HTTP,
  },
  // longpolling不需要会话粘性
});
```

### 3. ALB路由规则

配置基于路径的路由规则，优先级高于主应用路由：

```typescript
// 公网ALB longpolling路由（优先级90，高于主应用的100）
httpsListener.addAction('YherpPublicLongpollingAction', {
  priority: 90,
  conditions: [
    elbv2.ListenerCondition.hostHeaders([config.domains.applications.yherp.public]),
    elbv2.ListenerCondition.pathPatterns(['/longpolling/*', '/websocket/*']),
  ],
  action: elbv2.ListenerAction.forward([yherpLongpollingTargetGroup]),
});

// 内部ALB longpolling路由
httpListener.addAction('YherpInternalLongpollingAction', {
  priority: 90,
  conditions: [
    elbv2.ListenerCondition.hostHeaders([config.domains.applications.yherp.internal]),
    elbv2.ListenerCondition.pathPatterns(['/longpolling/*', '/websocket/*']),
  ],
  action: elbv2.ListenerAction.forward([yherpLongpollingTargetGroup]),
});
```

## ECS任务配置

### 1. 端口映射

在ECS任务定义中添加8072端口映射：

```typescript
// 添加主应用端口映射
container.addPortMappings({
  containerPort: 8069,
  hostPort: 0, // 动态端口映射
  protocol: ecs.Protocol.TCP,
});

// 添加longpolling端口映射
container.addPortMappings({
  containerPort: 8072,
  hostPort: 0, // 动态端口映射
  protocol: ecs.Protocol.TCP,
});
```

### 2. 环境变量配置

配置Odoo启用longpolling功能：

```typescript
environment: {
  HOST: databaseStack.pgpoolEndpoint,
  PORT: '5434',
  USER: 'odoo_admin',
  REDIS_HOST: databaseStack.redis.attrRedisEndpointAddress,
  REDIS_PORT: '6379',
  ODOO_RC: `/etc/odoo/odoo.conf`,
  // Longpolling配置
  LONGPOLLING_PORT: '8072',
  WORKERS: '2', // 启用多进程模式以支持longpolling
},
```

### 3. 服务注册

将ECS服务同时注册到主应用和longpolling目标组：

```typescript
// 将服务附加到目标组
this.yherpService.attachToApplicationTargetGroup(yherpTargetGroup);
this.yherpService.attachToApplicationTargetGroup(yherpLongpollingTargetGroup);
```

## Odoo应用配置

### 1. 配置文件设置

在Odoo配置文件中启用longpolling：

```ini
[options]
# 基础配置
addons_path = /mnt/extra-addons
data_dir = /var/lib/odoo

# 数据库配置
db_host = pgpool-endpoint
db_port = 5434
db_user = odoo_admin
db_password = your-password

# 多进程配置（必需）
workers = 2
max_cron_threads = 1

# Longpolling配置
longpolling_port = 8072
gevent_port = 8072

# 代理模式
proxy_mode = True
```

### 2. 启动参数

使用正确的启动参数：

```bash
# 主应用进程
odoo --workers=2 --longpolling-port=8072

# 或者使用环境变量
export WORKERS=2
export LONGPOLLING_PORT=8072
odoo
```

## 测试和验证

### 1. 健康检查测试

```bash
# 测试主应用端点
curl -I http://your-domain.com/web/health

# 测试longpolling端点
curl -I http://your-domain.com/longpolling/poll
```

### 2. 功能测试

1. **聊天功能测试**
   - 登录Odoo系统
   - 打开聊天窗口
   - 发送消息，验证实时接收

2. **通知测试**
   - 创建一个需要审批的记录
   - 验证相关用户实时收到通知

3. **多用户协作测试**
   - 多个用户同时编辑同一记录
   - 验证实时同步功能

### 3. 网络测试脚本

使用提供的测试脚本验证longpolling配置：

```bash
# 运行完整的网络测试
./scripts/test-network-routing.sh prod

# 专门测试longpolling端点
curl -s -o /dev/null -w "%{http_code}" https://dp.kh2u.com/longpolling/poll
```

## 故障排除

### 常见问题

1. **Longpolling连接失败**
   ```bash
   # 检查端口是否开放
   telnet your-domain.com 8072
   
   # 检查ALB目标组健康状态
   aws elbv2 describe-target-health --target-group-arn <longpolling-target-group-arn>
   ```

2. **实时功能不工作**
   - 检查浏览器开发者工具的网络标签
   - 查看是否有longpolling请求
   - 验证WebSocket连接状态

3. **性能问题**
   ```bash
   # 检查longpolling连接数
   netstat -an | grep :8072 | wc -l
   
   # 监控ECS任务资源使用
   aws ecs describe-services --cluster your-cluster --services yherp-prod
   ```

### 日志分析

```bash
# 查看Odoo应用日志
aws logs tail /aws/ecs/yherp-prod --follow

# 查看ALB访问日志中的longpolling请求
aws s3 ls s3://your-alb-logs-bucket/ | grep longpolling

# 查看CloudWatch指标
aws cloudwatch get-metric-statistics \
  --namespace AWS/ApplicationELB \
  --metric-name TargetResponseTime \
  --dimensions Name=TargetGroup,Value=yherp-longpoll-tg-prod
```

## 性能优化

### 1. 连接池配置

```ini
# Odoo配置优化
workers = 4  # 根据CPU核心数调整
max_cron_threads = 2
limit_memory_hard = **********  # 2.5GB
limit_memory_soft = 2147483648  # 2GB
limit_time_cpu = 600
limit_time_real = 1200
```

### 2. 负载均衡器优化

- 调整健康检查间隔
- 配置适当的超时时间
- 监控连接数和响应时间

### 3. 监控和告警

设置CloudWatch告警监控longpolling性能：

```bash
# 创建longpolling响应时间告警
aws cloudwatch put-metric-alarm \
  --alarm-name "Longpolling-High-Response-Time" \
  --alarm-description "Longpolling response time is high" \
  --metric-name TargetResponseTime \
  --namespace AWS/ApplicationELB \
  --statistic Average \
  --period 300 \
  --threshold 5.0 \
  --comparison-operator GreaterThanThreshold
```

## 安全考虑

### 1. 网络安全

- Longpolling端点仅通过ALB访问
- 配置适当的安全组规则
- 启用WAF保护

### 2. 应用安全

- 验证用户身份和权限
- 限制连接数和频率
- 监控异常连接模式

## 参考资料

- [Odoo 18官方部署文档](https://www.odoo.com/documentation/18.0/administration/on_premise/deploy.html)
- [AWS Application Load Balancer用户指南](https://docs.aws.amazon.com/elasticloadbalancing/latest/application/)
- [ECS服务发现和负载均衡](https://docs.aws.amazon.com/AmazonECS/latest/developerguide/service-load-balancing.html)

---

**注意**: 本配置基于Odoo 18的最新要求，确保longpolling功能在生产环境中稳定运行。如有问题，请参考官方文档或联系技术支持。
