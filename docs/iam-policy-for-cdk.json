{"Version": "2012-10-17", "Statement": [{"Sid": "CDKIAMPermissions", "Effect": "Allow", "Action": ["iam:CreateRole", "iam:DeleteRole", "iam:GetRole", "iam:AttachRolePolicy", "iam:DetachRolePolicy", "iam:PutRolePolicy", "iam:GetRolePolicy", "iam:DeleteRolePolicy", "iam:ListRolePolicies", "iam:ListAttachedRolePolicies", "iam:PassRole", "iam:TagRole", "iam:UntagRole", "iam:CreatePolicy", "iam:DeletePolicy", "iam:GetPolicy", "iam:GetPolicyVersion", "iam:ListPolicyVersions", "iam:CreatePolicyVersion", "iam:DeletePolicyVersion", "iam:SetDefaultPolicyVersion"], "Resource": ["arn:aws:iam::138264596682:role/cdk-*", "arn:aws:iam::138264596682:policy/cdk-*"]}, {"Sid": "CloudFormationPermissions", "Effect": "Allow", "Action": ["cloudformation:*"], "Resource": "*"}, {"Sid": "S3Permissions", "Effect": "Allow", "Action": ["s3:*"], "Resource": "*"}, {"Sid": "ECRPermissions", "Effect": "Allow", "Action": ["ecr:*"], "Resource": "*"}, {"Sid": "SSMPermissions", "Effect": "Allow", "Action": ["ssm:GetParameter", "ssm:PutParameter", "ssm:DeleteParameter", "ssm:GetParameters", "ssm:GetParametersByPath", "ssm:AddTagsToResource", "ssm:RemoveTagsFromResource"], "Resource": "*"}, {"Sid": "EC2Permissions", "Effect": "Allow", "Action": ["ec2:*"], "Resource": "*"}, {"Sid": "ECSPermissions", "Effect": "Allow", "Action": ["ecs:*"], "Resource": "*"}, {"Sid": "ELBPermissions", "Effect": "Allow", "Action": ["elasticloadbalancing:*"], "Resource": "*"}, {"Sid": "Route53Permissions", "Effect": "Allow", "Action": ["route53:*"], "Resource": "*"}, {"Sid": "ACMPermissions", "Effect": "Allow", "Action": ["acm:*"], "Resource": "*"}, {"Sid": "CloudWatchPermissions", "Effect": "Allow", "Action": ["cloudwatch:*", "logs:*"], "Resource": "*"}, {"Sid": "SNSPermissions", "Effect": "Allow", "Action": ["sns:*"], "Resource": "*"}, {"Sid": "WAFPermissions", "Effect": "Allow", "Action": ["wafv2:*"], "Resource": "*"}, {"Sid": "CloudFrontPermissions", "Effect": "Allow", "Action": ["cloudfront:*"], "Resource": "*"}, {"Sid": "ApplicationAutoScalingPermissions", "Effect": "Allow", "Action": ["application-autoscaling:*"], "Resource": "*"}, {"Sid": "SecretsManagerPermissions", "Effect": "Allow", "Action": ["secretsmanager:*"], "Resource": "*"}]}