# Apache Airflow专用数据库设置指南

本文档描述了如何为Apache Airflow创建专用的数据库和用户账号，确保与Odoo数据库完全隔离。

## 概述

### 实现的功能

1. **专用数据库创建**：
   - 开发环境：`airflow-dev` 数据库
   - 生产环境：`airflow` 数据库

2. **专用用户账号**：
   - 用户名：`airflow_user`
   - 最小权限原则：只授予Airflow运行所需的必要权限

3. **服务隔离**：
   - Airflow与Odoo数据库完全隔离
   - 独立的数据库凭证管理

4. **自动化部署**：
   - 使用AWS CDK Custom Resources
   - 通过ECS任务执行数据库初始化

## 架构组件

### 1. DatabaseInitConstruct

位置：`lib/constructs/database-init-construct.ts`

**功能**：
- 创建Airflow专用数据库凭证
- 定义ECS任务用于数据库初始化
- 创建Lambda函数协调数据库初始化过程
- 使用Custom Resources确保在数据库创建后执行初始化

**关键特性**：
- 支持开发和生产环境的不同数据库名称
- 自动生成安全的数据库密码
- 使用PostgreSQL容器执行数据库操作
- 完整的错误处理和日志记录

### 2. Aurora Database Stack更新

位置：`lib/stacks/aurora-database-stack.ts`

**更新内容**：
- 集成DatabaseInitConstruct
- 添加Airflow数据库凭证输出
- 确保数据库初始化在主数据库创建后执行

### 3. Airflow Stack更新

位置：`lib/stacks/airflow-stack.ts`

**更新内容**：
- 使用Airflow专用数据库凭证
- 更新数据库连接配置
- 确保使用正确的数据库名称和用户

## 部署步骤

### 1. 前置条件

确保已安装必要的工具：
```bash
# 检查Node.js版本
node --version  # >= 18.x

# 检查AWS CLI
aws --version   # >= 2.x

# 检查CDK
cdk --version   # >= 2.x

# 配置AWS凭证
aws configure
```

### 2. 部署基础设施

```bash
# 1. 安装依赖
npm install

# 2. 构建项目
npm run build

# 3. 设置环境变量
export NODE_ENV=dev  # 或 prod

# 4. 部署网络栈
npx cdk deploy YuanhuiNetwork-${NODE_ENV} --require-approval never

# 5. 部署ECS栈
npx cdk deploy YuanhuiEcs-${NODE_ENV} --require-approval never

# 6. 部署Aurora数据库栈（包含Airflow数据库初始化）
npx cdk deploy YuanhuiAuroraDatabase-${NODE_ENV} --require-approval never

# 7. 部署其他必要的栈
npx cdk deploy YuanhuiServiceConnect-${NODE_ENV} --require-approval never
npx cdk deploy YuanhuiRabbitMQ-${NODE_ENV} --require-approval never

# 8. 部署Airflow栈
npx cdk deploy YuanhuiAirflow-${NODE_ENV} --require-approval never
```

### 3. 验证部署

使用提供的验证脚本：
```bash
# 给脚本执行权限
chmod +x scripts/verify-airflow-database.sh

# 运行验证
./scripts/verify-airflow-database.sh
```

验证脚本将检查：
- 数据库连接
- Airflow数据库是否存在
- Airflow用户是否存在
- 用户权限是否正确
- 数据库隔离是否有效

## 配置详情

### 数据库配置

**开发环境**：
- 数据库名称：`airflow-dev`
- 用户名：`airflow_user`
- 主机：Aurora RDS实例端点
- 端口：5432

**生产环境**：
- 数据库名称：`airflow`
- 用户名：`airflow_user`
- 主机：Aurora集群端点
- 端口：5432

### 权限配置

Airflow用户被授予以下权限：
- `CONNECT` - 连接到Airflow数据库
- `USAGE` - 使用public schema
- `CREATE` - 在public schema中创建对象
- `ALL PRIVILEGES` - 对所有表和序列的完全权限
- `ALTER DEFAULT PRIVILEGES` - 对未来创建的对象的权限

### 安全特性

1. **密码管理**：
   - 自动生成32位随机密码
   - 存储在AWS Secrets Manager中
   - 排除特殊字符以避免连接问题

2. **网络安全**：
   - 数据库位于私有子网中
   - 通过安全组控制访问
   - Lambda函数使用专用安全组

3. **权限隔离**：
   - Airflow用户无法访问其他数据库
   - 最小权限原则
   - 独立的凭证管理

## 故障排除

### 常见问题

1. **数据库初始化失败**
   ```bash
   # 检查CloudFormation事件
   aws cloudformation describe-stack-events \
     --stack-name YuanhuiAuroraDatabase-${NODE_ENV}
   
   # 检查Lambda函数日志
   aws logs tail /aws/lambda/YuanhuiAuroraDatabase-${NODE_ENV}-DatabaseInitFunction --follow
   ```

2. **Airflow连接失败**
   ```bash
   # 验证数据库凭证
   aws secretsmanager get-secret-value \
     --secret-id $(aws cloudformation describe-stacks \
       --stack-name YuanhuiAuroraDatabase-${NODE_ENV} \
       --query 'Stacks[0].Outputs[?OutputKey==`AirflowDatabaseSecretArn`].OutputValue' \
       --output text)
   ```

3. **权限问题**
   ```bash
   # 手动连接数据库检查权限
   psql -h <database-endpoint> -U airflow_user -d airflow-dev
   \du  # 查看用户权限
   \l   # 查看数据库列表
   ```

### 重新初始化

如果需要重新初始化数据库：
```bash
# 1. 删除Custom Resource（触发重新创建）
aws cloudformation update-stack \
  --stack-name YuanhuiAuroraDatabase-${NODE_ENV} \
  --use-previous-template \
  --parameters ParameterKey=ForceReinit,ParameterValue=true

# 2. 或者完全重新部署数据库栈
npx cdk destroy YuanhuiAuroraDatabase-${NODE_ENV}
npx cdk deploy YuanhuiAuroraDatabase-${NODE_ENV} --require-approval never
```

## 监控和维护

### 日志位置

- **Lambda函数日志**：`/aws/lambda/YuanhuiAuroraDatabase-${NODE_ENV}-DatabaseInitFunction`
- **ECS任务日志**：`/aws/ecs/database-init`
- **Airflow日志**：`/aws/ecs/airflow-*`

### 定期检查

建议定期执行以下检查：
```bash
# 每周运行验证脚本
./scripts/verify-airflow-database.sh

# 检查数据库连接
aws rds describe-db-clusters --db-cluster-identifier <cluster-id>

# 检查Secrets Manager中的凭证
aws secretsmanager list-secrets --filters Key=name,Values=airflow
```

## 相关文档

- [Aurora Database Stack文档](./database/AURORA_SETUP.md)
- [Airflow部署指南](./airflow-deployment-guide.md)
- [安全最佳实践](./security/SECURITY_BEST_PRACTICES.md)
- [故障排除指南](./troubleshooting/DATABASE_ISSUES.md)
