# 元晖Odoo应用服务部署指南

## 前置条件

### 1. 环境要求
- Node.js 18.x 或更高版本
- AWS CLI 2.x
- AWS CDK 2.x
- Docker（用于本地测试）

### 2. AWS账户配置
```bash
# 配置AWS凭证
aws configure

# 验证配置
aws sts get-caller-identity
```

### 3. CDK环境准备
```bash
# 安装CDK
npm install -g aws-cdk

# 初始化CDK环境（首次使用）
cdk bootstrap aws://ACCOUNT-NUMBER/REGION
```

## 部署步骤

### 1. 克隆和安装依赖
```bash
# 克隆项目
git clone <repository-url>
cd yuanhui-odoo-iac

# 安装依赖
npm install
```

### 2. 环境配置

```bash
# 设置环境变量
export NODE_ENV=dev  # 或 prod
export CDK_DEFAULT_ACCOUNT=************
export CDK_DEFAULT_REGION=ap-east-2  # 香港区域

# 验证环境变量
echo "Environment: $NODE_ENV"
echo "Account: $CDK_DEFAULT_ACCOUNT"
echo "Region: $CDK_DEFAULT_REGION"
```

### 3. 编译TypeScript
```bash
npm run build
```

### 4. 验证配置
```bash
# 查看将要部署的资源
cdk diff
```

### 5. 部署基础设施

#### 开发环境部署

```bash
# 部署所有栈（推荐）
cdk deploy --all

# 或者按依赖顺序逐个部署
cdk deploy YuanhuiNetwork-dev      # 网络基础设施
cdk deploy YuanhuiEcs-dev          # ECS集群
cdk deploy YuanhuiDatabase-dev     # 容器化数据库
cdk deploy YuanhuiDns-dev          # DNS和SSL证书
cdk deploy YuanhuiApplication-dev  # 应用服务
cdk deploy YuanhuiMonitoring-dev   # 监控和告警

# 验证部署状态
aws cloudformation list-stacks --stack-status-filter CREATE_COMPLETE UPDATE_COMPLETE
```

#### 生产环境部署

```bash
# 设置生产环境
export NODE_ENV=prod

# 预览生产环境变更
cdk diff --all

# 部署生产环境（需要确认）
cdk deploy --all

# 或者无需确认的自动化部署
cdk deploy --all --require-approval never

# 验证生产环境部署
aws ecs describe-clusters --clusters yuanhui-odoo-prod
aws ecs describe-services --cluster yuanhui-odoo-prod --services yherp-prod khmall-prod
```

### 6. 验证部署
```bash
# 检查栈状态
aws cloudformation describe-stacks --stack-name YuanhuiApplication-dev

# 检查ECS服务状态
aws ecs describe-services --cluster yuanhui-odoo-dev --services yherp-dev khmall-dev
```

## 配置说明

### 环境变量

| 变量名 | 描述 | 默认值 | 必需 |
|--------|------|--------|------|
| NODE_ENV | 环境类型（dev/prod） | dev | 是 |
| CDK_DEFAULT_ACCOUNT | AWS账户ID | - | 是 |
| CDK_DEFAULT_REGION | AWS区域 | ap-east-2 | 是 |

### 配置文件详解

主要配置文件位于 `lib/config/environment.ts`：

```typescript
// 开发环境配置示例
export const devConfig: EnvironmentConfig = {
  region: 'ap-east-2',  // 香港区域
  account: process.env.CDK_DEFAULT_ACCOUNT || '',
  environment: 'dev',

  // VPC网络配置
  vpc: {
    cidr: '10.0.0.0/16',
    maxAzs: 2,
    natGateways: 1,
  },

  // 域名配置
  domains: {
    primary: { zoneName: 'kh2u.com' },
    applications: {
      yherp: {
        internal: 'yh.kh2u.com',  // 内部访问
        public: 'dp.kh2u.com'     // 公网访问
      },
      khmall: { domain: 'jmall.tw' }
    }
  },

  // ECS配置
  ecs: {
    cpu: 512,
    memory: 1024,
    desiredCount: 1,
    minCapacity: 1,
    maxCapacity: 2
  },

  // 容器化数据库配置
  database: {
    allocatedStorage: 20,
    maxAllocatedStorage: 100,
    backupRetention: 7,
    multiAz: false
  },

  // Redis配置
  redis: {
    nodeType: 'cache.t3.micro',
    numCacheNodes: 1
  }
};
```

## 应用配置

### 1. 容器化数据库初始化

部署完成后，需要初始化容器化PostgreSQL数据库：

```bash
# 获取数据库密钥
aws secretsmanager get-secret-value --secret-id yuanhui-database-secret-{env} --query SecretString --output text

# 连接到PgPool连接池（推荐方式）
# PgPool会自动路由到主数据库进行写操作
psql -h <pgpool-endpoint> -p 5434 -U odoo_admin -d postgres

# 或直接连接到PostgreSQL主服务器
psql -h <postgresql-master-endpoint> -p 5432 -U odoo_admin -d postgres

# 创建Odoo数据库
CREATE DATABASE yherp;
CREATE DATABASE khmall;

# 验证数据库创建
\l
```

### 2. Odoo应用配置

创建Odoo配置文件，应用会通过PgPool连接池访问数据库：

```ini
[options]
addons_path = /mnt/extra-addons
data_dir = /var/lib/odoo
admin_passwd = <admin-password>

# 数据库配置（通过PgPool连接池）
db_host = <pgpool-service-endpoint>
db_port = 5434
db_user = odoo_admin
db_password = <database-password>

# Redis会话配置
session_store = redis
redis_host = <redis-endpoint>
redis_port = 6379

# Longpolling配置
longpolling_port = 8072
workers = 2

# 日志配置
log_level = info
log_handler = :INFO
```

### 3. 多域名路由配置

本项目支持多域名自动路由，DNS记录会自动创建：

#### 生产环境路由

```dns
# 内部访问（通过OpenZiti零信任网络）
yh.kh2u.com → 内部ALB → Yherp应用

# 公网访问（通过WAF防护）
dp.kh2u.com → 公网ALB → Yherp应用

# 电商平台（通过CloudFront CDN）
jmall.tw → CloudFront → 公网ALB → Khmall应用
```

#### 开发环境路由

```dns
# 内部访问（基础配置）
yh-dev.kh2u.com → 内部ALB → Yherp应用

# 公网访问（通过WAF防护）
dp-dev.kh2u.com → 公网ALB → Yherp应用

# 电商平台（基础负载均衡）
jmall-dev.tw → 公网ALB → Khmall应用
```

**环境差异说明**:
- **OpenZiti零信任网络**: 仅生产环境启用
- **CloudFront CDN**: 仅生产环境启用
- **高级WAF规则**: 生产环境包含IP白名单和地理位置限制

### 4. Longpolling端口配置验证

验证Odoo longpolling功能是否正常：

```bash
# 检查容器端口映射
aws ecs describe-tasks --cluster yuanhui-odoo-{env} --tasks <task-arn>

# 测试主应用端口
curl -I http://<alb-endpoint>:8069/web/health

# 测试longpolling端口
curl -I http://<alb-endpoint>:8072/longpolling/health

# 检查负载均衡器目标组
aws elbv2 describe-target-groups --load-balancer-arn <alb-arn>
```

## 监控配置

### 1. CloudWatch仪表板

部署完成后，访问CloudWatch控制台查看自动创建的仪表板：

```url
https://console.aws.amazon.com/cloudwatch/home?region=ap-east-2#dashboards:name=yuanhui-odoo-{env}
```

### 2. 告警配置

配置SNS主题的邮件订阅：

```bash
# 订阅告警通知
aws sns subscribe \
  --topic-arn arn:aws:sns:ap-east-2:${CDK_DEFAULT_ACCOUNT}:yuanhui-alerts-{env} \
  --protocol email \
  --notification-endpoint <EMAIL>

# 验证订阅
aws sns list-subscriptions-by-topic \
  --topic-arn arn:aws:sns:ap-east-2:${CDK_DEFAULT_ACCOUNT}:yuanhui-alerts-{env}
```

### 3. 日志查看和监控

```bash
# 查看应用日志
aws logs describe-log-groups --log-group-name-prefix "/aws/ecs/yherp"
aws logs tail /aws/ecs/yherp-{env} --follow

# 查看数据库日志
aws logs tail /aws/ecs/postgresql-master-{env} --follow
aws logs tail /aws/ecs/pgpool-{env} --follow

# 查看WAF日志
aws logs tail /aws/wafv2/yuanhui-{env} --follow

# 查看OpenZiti日志（如果启用）
aws logs tail /aws/ecs/ziti-controller-{env} --follow
```

### 4. 性能监控指标

关键监控指标和阈值：

```bash
# 检查ECS服务指标
aws cloudwatch get-metric-statistics \
  --namespace AWS/ECS \
  --metric-name CPUUtilization \
  --dimensions Name=ServiceName,Value=yherp-{env} \
  --start-time $(date -u -d '1 hour ago' +%Y-%m-%dT%H:%M:%S) \
  --end-time $(date -u +%Y-%m-%dT%H:%M:%S) \
  --period 300 \
  --statistics Average

# 检查数据库连接数
aws cloudwatch get-metric-statistics \
  --namespace AWS/ECS \
  --metric-name DatabaseConnections \
  --dimensions Name=ServiceName,Value=postgresql-master-{env} \
  --start-time $(date -u -d '1 hour ago' +%Y-%m-%dT%H:%M:%S) \
  --end-time $(date -u +%Y-%m-%dT%H:%M:%S) \
  --period 300 \
  --statistics Average
```

## 故障排除

### 常见问题

#### 1. 部署失败
```bash
# 查看详细错误信息
cdk deploy --verbose

# 检查CloudFormation事件
aws cloudformation describe-stack-events --stack-name <stack-name>
```

#### 2. 服务无法启动
```bash
# 检查ECS任务状态
aws ecs describe-tasks --cluster <cluster-name> --tasks <task-arn>

# 查看容器日志
aws logs get-log-events --log-group-name <log-group> --log-stream-name <log-stream>
```

#### 3. 数据库连接问题
```bash
# 检查安全组规则
aws ec2 describe-security-groups --group-ids <security-group-id>

# 测试数据库连接
telnet <database-endpoint> 5432
```

## 更新和维护

### 1. 应用更新
```bash
# 更新应用镜像
aws ecs update-service --cluster <cluster-name> --service <service-name> --force-new-deployment
```

### 2. 基础设施更新
```bash
# 更新CDK代码后重新部署
npm run build
cdk diff
cdk deploy
```

### 3. 备份验证
```bash
# 检查备份状态
aws backup describe-backup-jobs --by-backup-vault-name <backup-vault-name>
```

## 安全最佳实践

### 1. 密钥管理
- 使用AWS Secrets Manager存储敏感信息
- 定期轮换密钥
- 启用密钥审计

### 2. 网络安全
- 定期审查安全组规则
- 启用VPC Flow Logs
- 使用WAF保护应用

### 3. 访问控制
- 使用IAM角色而非用户密钥
- 实施最小权限原则
- 启用CloudTrail审计

## 成本优化

### 1. 资源监控
```bash
# 查看成本和使用情况
aws ce get-cost-and-usage --time-period Start=2024-01-01,End=2024-01-31 --granularity MONTHLY --metrics BlendedCost
```

### 2. 优化建议
- 使用预留实例降低成本
- 定期清理未使用的资源
- 监控和优化数据传输成本

## 下一步

- [运维指南](../operations/README.md)
- [故障排除指南](../troubleshooting/README.md)
- [性能优化指南](../performance/README.md)
