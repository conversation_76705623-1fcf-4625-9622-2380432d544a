# 元晖基础设施升级部署指南

## 📋 部署前准备

### 环境要求
- Node.js >= 18.x
- AWS CLI >= 2.x
- CDK CLI >= 2.x
- 有效的AWS凭证配置

### 权限要求
确保AWS账户具有以下权限：
- RDS (Aurora)
- ECS
- EC2
- VPC
- EFS
- Secrets Manager
- CloudWatch
- IAM

### 预部署检查
```bash
# 1. 验证AWS凭证
aws sts get-caller-identity

# 2. 检查CDK版本
npx cdk --version

# 3. 验证区域配置
echo $CDK_DEFAULT_REGION  # 应为 ap-east-2

# 4. 编译项目
npm run build
```

## 🚀 分阶段部署策略

### 阶段1: 基础设施部署 (15-20分钟)

#### 1.1 网络基础设施
```bash
export NODE_ENV=dev
export CDK_DEFAULT_ACCOUNT=$(aws sts get-caller-identity --query Account --output text)
export CDK_DEFAULT_REGION=ap-east-2

# 部署VPC、安全组、WAF
npx cdk deploy YuanhuiNetwork-dev --require-approval never
```

**预期结果**:
- VPC创建完成
- 安全组配置就绪
- NAT网关运行中

#### 1.2 ECS集群
```bash
# 部署ECS集群和自动扩容组
npx cdk deploy YuanhuiEcs-dev --require-approval never
```

**预期结果**:
- ECS集群创建
- EC2实例启动
- 容量提供者配置

#### 1.3 Service Connect命名空间
```bash
# 部署服务发现命名空间
npx cdk deploy YuanhuiServiceConnect-dev --require-approval never
```

**预期结果**:
- Service Connect命名空间创建
- 服务发现就绪

### 阶段2: 数据库部署 (10-15分钟)

#### 2.1 Aurora Serverless v2
```bash
# 部署Aurora PostgreSQL集群
npx cdk deploy YuanhuiAuroraDatabase-dev --require-approval never
```

**预期结果**:
- Aurora集群创建
- Writer和Reader实例启动
- 数据库凭证生成
- Redis缓存部署

**验证步骤**:
```bash
# 检查Aurora集群状态
aws rds describe-db-clusters --db-cluster-identifier $(aws rds describe-db-clusters --query 'DBClusters[0].DBClusterIdentifier' --output text)

# 检查Redis状态
aws elasticache describe-cache-clusters --show-cache-node-info
```

### 阶段3: 中间件部署 (8-12分钟)

#### 3.1 RabbitMQ消息队列
```bash
# 部署RabbitMQ服务
npx cdk deploy YuanhuiRabbitMQ-dev --require-approval never
```

**预期结果**:
- RabbitMQ容器启动
- 管理界面可访问
- Service Connect注册

**验证步骤**:
```bash
# 检查RabbitMQ服务状态
aws ecs describe-services --cluster yuanhui-odoo-dev --services rabbitmq-dev

# 获取管理界面URL
aws cloudformation describe-stacks --stack-name YuanhuiRabbitMQ-dev --query 'Stacks[0].Outputs[?OutputKey==`RabbitMQManagementURL`].OutputValue' --output text
```

#### 3.2 Apache Airflow工作流引擎
```bash
# 部署Airflow服务
npx cdk deploy YuanhuiAirflow-dev --require-approval never
```

**预期结果**:
- EFS文件系统创建
- Airflow Webserver启动
- Scheduler和Worker运行
- 数据库初始化完成

**验证步骤**:
```bash
# 检查Airflow服务状态
aws ecs describe-services --cluster yuanhui-odoo-dev --services airflow-webserver-dev airflow-scheduler-dev

# 获取Airflow Web界面URL
aws cloudformation describe-stacks --stack-name YuanhuiAirflow-dev --query 'Stacks[0].Outputs[?OutputKey==`AirflowWebserverURL`].OutputValue' --output text
```

### 阶段4: 应用部署 (10-15分钟)

#### 4.1 DNS和SSL证书
```bash
# 部署DNS和证书
npx cdk deploy YuanhuiDns-dev --require-approval never
```

**注意**: 可能需要手动验证域名所有权

#### 4.2 Odoo应用服务
```bash
# 部署Odoo应用
npx cdk deploy YuanhuiApplication-dev --require-approval never
```

**预期结果**:
- Yherp服务启动
- Khmall服务启动
- Cron服务运行
- 负载均衡器配置

### 阶段5: 监控部署 (5-8分钟)

```bash
# 部署监控和告警
npx cdk deploy YuanhuiMonitoring-dev --require-approval never
```

**预期结果**:
- CloudWatch仪表板创建
- 告警规则配置
- SNS主题设置

## 🔍 部署后验证

### 1. 服务健康检查

#### Aurora数据库
```bash
# 检查集群状态
aws rds describe-db-clusters --query 'DBClusters[0].Status'

# 检查连接
psql -h $(aws rds describe-db-clusters --query 'DBClusters[0].Endpoint' --output text) -U odoo_admin -d odoo
```

#### RabbitMQ
```bash
# 检查服务状态
curl -u admin:$(aws secretsmanager get-secret-value --secret-id $(aws cloudformation describe-stacks --stack-name YuanhuiRabbitMQ-dev --query 'Stacks[0].Outputs[?OutputKey==`RabbitMQSecretArn`].OutputValue' --output text) --query 'SecretString' --output text | jq -r '.password') http://rabbitmq-management-url/api/overview
```

#### Airflow
```bash
# 访问Web界面
curl -I http://airflow-webserver-url:8080/health
```

### 2. 应用功能验证

#### Odoo服务
```bash
# 检查Yherp服务
curl -I http://yherp-alb-url/web/health

# 检查Khmall服务  
curl -I http://khmall-alb-url/web/health
```

### 3. 监控验证
- 访问CloudWatch仪表板
- 检查告警配置
- 验证指标收集

## 🚨 故障排除

### 常见问题

#### 1. Aurora连接失败
```bash
# 检查安全组规则
aws ec2 describe-security-groups --group-ids $(aws rds describe-db-clusters --query 'DBClusters[0].VpcSecurityGroups[0].VpcSecurityGroupId' --output text)

# 检查子网组
aws rds describe-db-subnet-groups
```

#### 2. ECS服务启动失败
```bash
# 查看服务事件
aws ecs describe-services --cluster yuanhui-odoo-dev --services service-name --query 'services[0].events'

# 查看任务日志
aws logs describe-log-groups --log-group-name-prefix /aws/ecs/
```

#### 3. Service Connect连接问题
```bash
# 检查命名空间
aws servicediscovery list-namespaces

# 检查服务注册
aws servicediscovery list-services --filters Name=NAMESPACE_ID,Values=namespace-id
```

### 回滚策略

#### 紧急回滚
```bash
# 停止新服务
aws ecs update-service --cluster yuanhui-odoo-dev --service service-name --desired-count 0

# 恢复旧配置
# (需要预先备份旧的配置)
```

#### 完整回滚
```bash
# 删除新栈（按相反顺序）
npx cdk destroy YuanhuiMonitoring-dev
npx cdk destroy YuanhuiApplication-dev
npx cdk destroy YuanhuiAirflow-dev
npx cdk destroy YuanhuiRabbitMQ-dev
npx cdk destroy YuanhuiAuroraDatabase-dev
```

## 📊 性能调优

### Aurora优化
- 调整Serverless v2容量范围
- 配置Performance Insights
- 优化连接池设置

### ECS优化
- 调整任务CPU/内存配置
- 配置自动扩容策略
- 优化健康检查参数

### 网络优化
- 配置Service Connect负载均衡
- 优化安全组规则
- 调整ALB健康检查

## 📝 部署清单

### 部署前
- [ ] AWS凭证配置
- [ ] 环境变量设置
- [ ] 代码编译成功
- [ ] 权限验证通过

### 部署中
- [ ] 网络栈部署成功
- [ ] ECS集群运行正常
- [ ] Aurora集群创建完成
- [ ] 中间件服务启动
- [ ] 应用服务运行
- [ ] 监控配置完成

### 部署后
- [ ] 服务健康检查通过
- [ ] 数据库连接正常
- [ ] 应用功能验证
- [ ] 监控告警测试
- [ ] 性能基准测试

## 🔄 维护计划

### 日常维护
- 监控Aurora性能指标
- 检查RabbitMQ队列状态
- 验证Airflow DAG执行
- 审查CloudWatch告警

### 定期维护
- Aurora自动备份验证
- EFS存储使用监控
- 安全补丁更新
- 性能优化调整

---

## 📞 支持联系

如遇到部署问题，请联系：
- 技术支持：<EMAIL>
- 紧急联系：<EMAIL>

部署完成后，请更新运维文档并通知相关团队。
