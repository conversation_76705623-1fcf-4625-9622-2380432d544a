# Wiki.js 部署配置指南

## 数据库密钥配置

Wiki.js 需要连接到现有的 PostgreSQL 数据库。数据库连接信息必须存储在 AWS Secrets Manager 中。

### 1. 创建数据库密钥

在 AWS Secrets Manager 中创建一个包含以下字段的密钥：

```json
{
  "host": "your-database-host.amazonaws.com",
  "port": "5432",
  "username": "wikijs_user",
  "password": "your-secure-password"
}
```

**注意**：根据Wiki.js官方文档，数据库名称(DB_NAME)应该在配置中设置，而不是从AWS Secrets Manager获取。默认数据库名称为 `wiki`。

### 2. 配置环境变量

在部署前设置环境变量：

```bash
# 开发环境
export WIKIJS_DATABASE_SECRET_ARN="arn:aws:secretsmanager:ap-east-2:your-account:secret:wikijs-db-dev-xxxxxx"

# 生产环境
export WIKIJS_DATABASE_SECRET_ARN="arn:aws:secretsmanager:ap-east-2:your-account:secret:wikijs-db-prod-xxxxxx"
```

### 3. 部署 Wiki.js 栈

```bash
# 部署开发环境
npm run deploy:dev -- --stacks WikiJS

# 部署生产环境
npm run deploy:prod -- --stacks WikiJS
```

## 密钥字段说明

| 字段 | 描述 | 示例 |
|------|------|------|
| `host` | 数据库主机地址 | `aurora-cluster.cluster-xxx.ap-east-2.rds.amazonaws.com` |
| `port` | 数据库端口 | `5432` |
| `username` | 数据库用户名 | `wikijs_user` |
| `password` | 数据库密码 | `secure-password-123` |

**注意**：数据库名称(`dbname`)不需要在Secret中配置，它在应用配置中直接设置为 `wiki`。

## 数据库准备

在部署 Wiki.js 之前，确保：

1. PostgreSQL 数据库已创建并可访问
2. 已创建专用的数据库用户（如 `wikijs_user`）
3. 用户具有对指定数据库的完整访问权限
4. 网络安全组允许从 ECS 集群访问数据库

## 故障排除

### 密钥格式错误
确保密钥中的所有字段都是字符串格式，包括端口号。**注意**：不要在密钥中包含 `dbname` 字段。

### 连接失败
检查：
- 数据库主机地址是否正确
- 安全组规则是否允许连接
- 数据库用户权限是否足够
- 密钥 ARN 是否正确配置

### 权限问题
Wiki.js 栈会自动给 ECS 任务执行角色添加访问指定 Secret 的权限。如果遇到权限错误，请确保：

1. **Secret ARN 正确**：配置中的 `secretArn` 必须是完整的 ARN
2. **Secret 存在**：确保 Secret 在指定区域内存在
3. **重新部署栈**：权限更改需要重新部署 Wiki.js 栈才能生效

CDK 会自动添加以下权限策略：

```json
{
  "Version": "2012-10-17",
  "Statement": [
    {
      "Effect": "Allow",
      "Action": [
        "secretsmanager:GetSecretValue"
      ],
      "Resource": "arn:aws:secretsmanager:region:account:secret:your-secret-arn"
    }
  ]
}
```