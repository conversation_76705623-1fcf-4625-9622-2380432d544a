# 项目结构说明

## 概述

本项目已经完成了TypeScript/JavaScript文件结构的优化，实现了源码与编译输出的分离。

## 目录结构

```
iac/
├── bin/                    # 可执行文件源码
│   └── iac.ts             # CDK CLI入口文件
├── lib/                    # 主要源码目录
│   ├── config/            # 配置文件
│   │   └── environment.ts # 环境配置
│   ├── constructs/        # 自定义构造
│   │   └── backup-construct.ts
│   ├── stacks/            # CDK栈定义
│   │   ├── application-stack.ts
│   │   ├── database-stack.ts
│   │   ├── ecs-stack.ts
│   │   ├── monitoring-stack.ts
│   │   └── network-stack.ts
│   └── iac-stack.ts       # 主栈文件
├── test/                   # 测试文件
│   └── iac.test.ts        # 单元测试
├── dist/                   # 编译输出目录（自动生成）
│   ├── bin/               # 编译后的可执行文件
│   ├── lib/               # 编译后的主要代码
│   └── test/              # 编译后的测试文件
├── docs/                   # 文档目录
├── scripts/                # 脚本目录
├── cdk.out/               # CDK输出目录（自动生成）
├── node_modules/          # 依赖包（自动生成）
├── tsconfig.json          # TypeScript配置
├── package.json           # 项目配置
└── .gitignore            # Git忽略规则
```

## 主要改进

### 1. TypeScript配置优化

**tsconfig.json** 主要变更：
- 添加 `"outDir": "./dist"` - 指定编译输出目录
- 添加 `"declarationDir": "./dist"` - 指定类型声明文件输出目录
- 添加 `"rootDir": "./"` - 指定源码根目录
- 添加 `"include"` 配置 - 明确指定需要编译的文件
- 更新 `"exclude"` 配置 - 排除dist目录

### 2. Git忽略规则优化

**.gitignore** 主要变更：
- 忽略整个 `dist/` 目录
- 精确忽略源码目录中的编译输出文件
- 保留必要的配置文件（如jest.config.js）
- 添加TypeScript增量编译信息忽略

### 3. 构建脚本优化

**package.json** 主要变更：
- 更新bin路径指向 `dist/bin/iac.js`
- 添加 `clean` 脚本用于清理输出目录
- 添加 `prebuild` 脚本在构建前自动清理

## 构建流程

### 开发流程

1. **清理并构建**：
   ```bash
   npm run build
   ```

2. **监听模式开发**：
   ```bash
   npm run watch
   ```

3. **运行测试**：
   ```bash
   npm test
   ```

4. **CDK命令**：
   ```bash
   npm run cdk -- list
   npm run cdk -- deploy
   ```

### 目录说明

- **源码目录** (`bin/`, `lib/`, `test/`)：只包含TypeScript源文件
- **输出目录** (`dist/`)：包含所有编译生成的JavaScript和类型声明文件
- **CDK输出** (`cdk.out/`)：CDK合成的CloudFormation模板
- **文档目录** (`docs/`)：项目文档和架构说明

## 版本控制

- ✅ 源码文件 (`.ts`) - 被跟踪
- ✅ 配置文件 (`.json`, `.md`) - 被跟踪
- ❌ 编译输出 (`dist/`) - 被忽略
- ❌ CDK输出 (`cdk.out/`) - 被忽略
- ❌ 依赖包 (`node_modules/`) - 被忽略

## 注意事项

1. **首次克隆项目后**：
   ```bash
   npm install
   npm run build
   ```

2. **部署前确保构建**：
   ```bash
   npm run build
   npm run cdk -- deploy
   ```

3. **清理输出文件**：
   ```bash
   npm run clean
   ```

这种结构确保了：
- 源码目录保持整洁，只包含TypeScript文件
- 编译输出统一管理在dist目录
- 版本控制只跟踪源码，不跟踪生成文件
- 构建流程清晰，易于维护
