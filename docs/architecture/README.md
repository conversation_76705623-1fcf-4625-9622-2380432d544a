# 元晖Odoo应用服务AWS架构文档

## 架构概述

本项目实现了一个完整的AWS云原生Odoo应用服务架构，采用基础设施即代码（IaC）的方式，使用AWS CDK进行部署和管理。架构采用容器化部署，支持多域名路由、零信任网络访问、数据库读写分离、自动扩容和全面的安全防护。

### 核心设计原则

- **容器化优先**: 所有服务均采用容器化部署，包括PostgreSQL数据库
- **基础设施即代码**: 使用AWS CDK (TypeScript) 实现完整的基础设施定义
- **多层安全防护**: 网络隔离 + WAF防护 + 零信任网络 + 数据加密
- **高可用性**: 多可用区部署 + 自动故障转移 + 健康检查
- **可观测性**: 全面的监控、日志和告警体系

## 架构组件

### 1. 网络层 (Network Layer)
- **VPC**: 自定义虚拟私有云，提供隔离的网络环境
- **子网**: 
  - 公有子网：用于负载均衡器和NAT网关
  - 私有子网：用于应用服务器
  - 数据库子网：用于数据库实例（隔离子网）
- **安全组**: 精细化的网络访问控制
- **NAT网关**: 为私有子网提供出站互联网访问

### 2. 数据库层 (Database Layer)

#### 容器化PostgreSQL集群
- **PostgreSQL 15主服务器**:
  - 处理所有写操作和事务
  - 运行在ECS EC2集群上
  - 使用EBS GP3卷持久化存储
  - 端口5432，固定主机端口映射
- **PostgreSQL 15从服务器**:
  - 处理只读查询，实现读写分离
  - 流复制同步数据
  - 端口5433，固定主机端口映射
- **PgPool-II 4.4连接池**:
  - 智能路由：写操作→主服务器，读操作→从服务器
  - 连接池管理，提高数据库性能
  - 负载均衡和故障转移
  - 端口5434，应用连接入口

#### Redis缓存集群
- **Redis 7.x**: 用于会话存储和应用缓存
- **ElastiCache部署**: 高可用性和自动故障转移
- **多节点配置**: 生产环境支持集群模式

### 3. 应用服务层 (Application Layer)

#### ECS EC2集群架构
- **计算资源**: EC2实例（t3.medium）+ 自动扩容组
- **容器编排**: Amazon ECS（EC2模式，非Fargate）
- **实例管理**: ECS优化AMI + 自动扩容策略
- **容量提供者**: ASG容量提供者，目标容量80%

#### 应用服务组件
- **Yherp应用服务**:
  - 面向内部用户的ERP系统
  - 支持多域名访问（yh.kh2u.com + dp.kh2u.com）
  - Odoo 16容器 + longpolling支持（8072端口）
- **Khmall应用服务**:
  - 面向外部客户的电商平台
  - 域名：jmall.tw + CloudFront CDN加速
  - 会话粘性支持多数据库架构
- **Odoo Cron服务**:
  - 后台任务和定时作业处理
  - 独立容器部署，避免影响主应用性能

#### 自动扩容策略
- **CPU阈值**: > 70%时触发扩容
- **内存阈值**: > 80%时触发扩容
- **扩容范围**: 最小1个实例，最大10个实例（生产环境）
- **缩容策略**: 基于平均利用率的智能缩容

### 4. 负载均衡和路由层 (Load Balancing & Routing Layer)

#### 多域名路由架构
- **公网Application Load Balancer**:
  - 处理dp.kh2u.com（Yherp公网访问）
  - 处理jmall.tw（Khmall电商平台）
  - 集成AWS WAF v2防护
  - SSL/TLS终止和证书管理
- **内网Application Load Balancer**:
  - 处理yh.kh2u.com（Yherp内网访问）
  - 集成OpenZiti零信任网络
  - 内部安全组限制访问

#### 路由策略
- **基于主机头路由**: 根据域名自动路由到对应应用
- **Longpolling路由**: 8072端口的WebSocket连接支持
- **健康检查**: 多层健康检查确保服务可用性
- **会话粘性**: 支持Odoo多数据库架构的会话保持

#### 安全防护层
- **AWS WAF v2**:
  - AWS托管规则集（Core Rule Set, Known Bad Inputs）
  - 自定义规则（IP白名单、地理位置限制）
  - 实时攻击监控和阻断
- **CloudFront CDN**（jmall.tw）:
  - 全球边缘节点加速
  - DDoS防护和缓存优化
  - 静态资源CDN分发

### 5. 零信任网络层 (Zero Trust Network)

#### OpenZiti架构
- **Ziti Controller**:
  - 网络控制平面，管理身份和策略
  - 运行在ECS容器中
  - 提供管理API和Web控制台
- **Ziti Router**:
  - 数据平面路由器，处理加密隧道
  - 部署在私有子网中
  - 提供安全的网络连接
- **身份和策略管理**:
  - 基于身份的访问控制（IBAC）
  - 细粒度的网络策略
  - 端到端加密通信

#### 访问控制策略
- **内部用户访问**: yh.kh2u.com通过OpenZiti网络
- **设备认证**: 客户端证书和身份验证
- **网络分段**: 应用级别的网络隔离
- **审计日志**: 完整的访问审计和监控

### 6. 监控和日志 (Monitoring & Logging)

#### CloudWatch监控体系
- **应用指标**: CPU、内存、响应时间、错误率
- **数据库指标**: 连接数、查询性能、PgPool状态
- **网络指标**: 负载均衡器健康状态、延迟
- **安全指标**: WAF阻断、OpenZiti连接状态
- **自定义仪表板**: 实时监控面板和趋势分析

#### 日志聚合和分析
- **应用日志**: ECS容器日志统一收集
- **数据库日志**: PostgreSQL和PgPool日志
- **网络日志**: VPC Flow Logs、ALB访问日志
- **安全日志**: WAF日志、CloudTrail审计日志
- **告警通知**: SNS多渠道告警（邮件、短信、Slack）

### 6. 备份和灾难恢复 (Backup & Disaster Recovery)
- **AWS Backup**: 自动化备份策略
- **多层备份**:
  - 每日备份（保留30天）
  - 每周备份（保留1年，仅生产环境）
  - 每月备份（保留7年，仅生产环境）
- **跨区域备份**（生产环境）
- **自动化恢复流程**

## 安全特性

### 网络安全
- VPC隔离
- 安全组最小权限原则
- 私有子网部署应用
- NAT网关控制出站流量

### 数据安全
- 数据库加密存储
- 传输加密
- 密钥管理（AWS Secrets Manager）
- 备份加密

### 访问控制
- IAM角色和策略
- 最小权限原则
- 服务间认证

## 高可用性设计

### 多可用区部署
- 应用服务跨多个可用区
- 数据库主从复制
- 负载均衡器多可用区

### 自动故障转移
- 数据库自动故障转移
- 应用服务自动重启
- 健康检查和自动替换

### 弹性扩容
- 基于指标的自动扩容
- 预定义扩容策略
- 快速响应负载变化

## 成本优化

### 资源优化
- 按需扩容，避免资源浪费
- 开发环境使用较小实例
- 生产环境使用预留实例

### 存储优化
- 数据库存储自动扩容
- 备份生命周期管理
- 冷存储策略

## 环境配置

### 开发环境 (dev)
- 单可用区部署
- 较小的实例规格
- 简化的备份策略
- 基础监控

### 生产环境 (prod)
- 多可用区部署
- 高性能实例规格
- 完整的备份策略
- 详细监控和告警

## 部署架构图

### 整体架构图

```
┌─────────────────────────────────────────────────────────────────────┐
│                           Internet                                  │
│  ┌─────────────────┐  ┌─────────────────┐  ┌─────────────────┐     │
│  │  yh.kh2u.com    │  │  dp.kh2u.com    │  │   jmall.tw      │     │
│  │ (OpenZiti)      │  │   (WAF)         │  │ (CloudFront)    │     │
│  └─────────────────┘  └─────────────────┘  └─────────────────┘     │
└─────────────┬─────────────────┬─────────────────┬───────────────────┘
              │                 │                 │
┌─────────────▼─────────────────▼─────────────────▼───────────────────┐
│                        Public Subnets                              │
│  ┌─────────────────┐  ┌─────────────────┐  ┌─────────────────┐     │
│  │  Internal ALB   │  │   Public ALB    │  │   NAT Gateway   │     │
│  │ (yh.kh2u.com)   │  │(dp/jmall域名)   │  │                 │     │
│  └─────────────────┘  └─────────────────┘  └─────────────────┘     │
└─────────────┬─────────────────┬─────────────────────────────────────┘
              │                 │
┌─────────────▼─────────────────▼─────────────────────────────────────┐
│                        Private Subnets                             │
│                    ECS EC2 Cluster                                 │
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐│
│  │   Yherp     │  │   Khmall    │  │ Odoo Cron   │  │ PostgreSQL  ││
│  │ Container   │  │ Container   │  │ Container   │  │   Master    ││
│  │ (8069/8072) │  │ (8069/8072) │  │             │  │  (5432)     ││
│  └─────────────┘  └─────────────┘  └─────────────┘  └─────────────┘│
│                                                                     │
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐│
│  │ PostgreSQL  │  │  PgPool-II  │  │    Redis    │  │  OpenZiti   ││
│  │   Replica   │  │ Connection  │  │   Cache     │  │ Controller  ││
│  │   (5433)    │  │ Pool (5434) │  │             │  │             ││
│  └─────────────┘  └─────────────┘  └─────────────┘  └─────────────┘│
└─────────────────────────────────────────────────────────────────────┘
```

### 数据流图

```
用户请求流:
Internet → CloudFront/WAF → ALB → ECS Container → PgPool → PostgreSQL
    │                                    │
    ▼                                    ▼
OpenZiti Network                    Redis Cache
    │                                    │
    ▼                                    ▼
内部用户访问                        会话存储

监控流:
ECS Containers → CloudWatch Logs → CloudWatch Metrics → SNS Alerts
PostgreSQL → CloudWatch Logs → Custom Metrics → Dashboard
WAF → CloudWatch Logs → Security Monitoring → Alerts
```

## 技术特性总结

### 容器化优势
- **统一部署**: 所有服务均采用容器化，包括数据库
- **资源隔离**: 每个服务独立运行，互不影响
- **弹性扩容**: 基于负载自动调整容器数量
- **版本管理**: 容器镜像版本化管理和回滚

### 高可用性保障
- **多可用区部署**: 跨AZ的容器和数据库部署
- **自动故障转移**: PostgreSQL主从切换和PgPool故障转移
- **健康检查**: 多层健康检查确保服务可用性
- **负载均衡**: 智能流量分发和会话保持

### 安全防护体系
- **多层防护**: 网络层 + 应用层 + 数据层安全
- **零信任网络**: OpenZiti端到端加密和身份验证
- **Web应用防护**: WAF规则和DDoS防护
- **数据加密**: 传输和存储全程加密

## 下一步

请参考以下文档继续了解：

- [部署指南](../deployment/README.md) - 完整部署流程和配置说明
- [运维指南](../operations/README.md) - 日常运维和监控管理
- [网络配置指南](../network/README.md) - 多域名路由和安全配置
- [故障排除指南](../network/troubleshooting-guide.md) - 常见问题和解决方案
