# 元晖Odoo应用服务升级架构文档

## 架构升级概述

本次架构升级将原有的自管理容器化PostgreSQL方案替换为AWS托管服务，并引入了现代化的中间件和工作流引擎，大幅提升了系统的可靠性、可扩展性和可维护性。

## 🔄 主要升级内容

### 1. 数据库架构升级
- **原架构**: 容器化PostgreSQL主从复制 + PgPool-II连接池
- **新架构**: AWS RDS Aurora Serverless v2 PostgreSQL集群
- **优势**:
  - 自动扩缩容（0.5-16 ACU）
  - 内置读写分离和负载均衡
  - 自动故障转移和备份
  - Performance Insights监控
  - 自动密码轮换

### 2. 中间件层引入
- **RabbitMQ 3.12**: 企业级消息队列
  - 支持集群模式（生产环境）
  - 管理界面访问
  - 持久化消息存储
  - 高可用配置

### 3. 工作流引擎部署
- **Apache Airflow 3.0**: 现代化工作流编排
  - Webserver + Scheduler + Worker架构
  - EFS共享存储（DAGs和日志）
  - 支持LocalExecutor和CeleryExecutor
  - 与RabbitMQ集成

### 4. 服务通信优化
- **ECS Service Connect**: 统一服务发现和通信
  - 替代传统的服务发现
  - 内置负载均衡和健康检查
  - 简化服务间通信配置

## 🏗️ 新架构组件详解

### 数据库层 (Aurora Serverless v2)
```
Aurora PostgreSQL 15.4 集群
├── Writer实例 (Serverless v2)
│   ├── 自动扩缩容: 0.5-16 ACU
│   ├── 读写操作处理
│   └── 自动故障转移
├── Reader实例 (Serverless v2) 
│   ├── 只读查询负载均衡
│   ├── 跨可用区分布
│   └── 自动扩缩容
└── 集群特性
    ├── 自动备份 (7-30天)
    ├── 加密存储和传输
    ├── Performance Insights
    └── 自动密码轮换
```

### 中间件层 (RabbitMQ)
```
RabbitMQ 3.12 集群
├── 消息队列服务
│   ├── AMQP协议支持
│   ├── 持久化消息存储
│   └── 高可用集群模式
├── 管理界面
│   ├── Web UI访问
│   ├── 监控和管理
│   └── 内部负载均衡器
└── 集成配置
    ├── ECS Service Connect
    ├── Secrets Manager凭证
    └── CloudWatch监控
```

### 工作流引擎 (Apache Airflow)
```
Apache Airflow 3.0 架构
├── Webserver组件
│   ├── Web UI界面
│   ├── API服务
│   └── 用户认证
├── Scheduler组件
│   ├── DAG调度
│   ├── 任务分发
│   └── 状态管理
├── Worker组件 (CeleryExecutor)
│   ├── 任务执行
│   ├── 水平扩展
│   └── 资源隔离
└── 存储配置
    ├── EFS共享文件系统
    ├── DAGs同步
    └── 日志集中存储
```

## 🔗 服务通信架构

### ECS Service Connect拓扑
```
Service Connect命名空间: dev-services
├── 应用服务
│   ├── yherp:8069 (Web)
│   ├── yherp-longpolling:8072
│   ├── khmall:8069 (Web)
│   ├── khmall-longpolling:8072
│   └── odoo-cron (客户端)
├── 中间件服务
│   ├── rabbitmq:5672 (AMQP)
│   ├── rabbitmq-management:15672
│   └── airflow-webserver:8080
└── 数据库服务
    ├── Aurora集群端点
    └── Redis缓存
```

## 📊 监控和可观测性

### Aurora数据库监控
- CPU使用率告警 (>80%)
- 连接数告警 (>80%)
- Performance Insights
- 自动备份监控

### 中间件监控
- RabbitMQ队列深度
- 消息处理速率
- 连接数和内存使用

### Airflow监控
- DAG执行状态
- 任务失败率
- 调度延迟
- Worker资源使用

## 🚀 部署顺序

推荐的部署顺序以避免依赖问题：

```bash
# 1. 基础网络设施
npx cdk deploy YuanhuiNetwork-dev

# 2. ECS集群
npx cdk deploy YuanhuiEcs-dev

# 3. Service Connect命名空间
npx cdk deploy YuanhuiServiceConnect-dev

# 4. Aurora数据库
npx cdk deploy YuanhuiAuroraDatabase-dev

# 5. RabbitMQ消息队列
npx cdk deploy YuanhuiRabbitMQ-dev

# 6. Airflow工作流引擎
npx cdk deploy YuanhuiAirflow-dev

# 7. DNS和SSL证书
npx cdk deploy YuanhuiDns-dev

# 8. Odoo应用服务
npx cdk deploy YuanhuiApplication-dev

# 9. 监控和告警
npx cdk deploy YuanhuiMonitoring-dev
```

## 🔧 配置要点

### 环境变量配置
新增的主要配置项：

```typescript
// RabbitMQ配置
rabbitmq: {
  cpu: 512,
  memory: 1024,
  storageSize: 10,
  enableManagementUI: true,
  managementPort: 15672,
  enableClustering: false,
  replicas: 1,
}

// Airflow配置
airflow: {
  version: '3.0.0',
  webserver: { cpu: 512, memory: 1024, replicas: 1, port: 8080 },
  scheduler: { cpu: 512, memory: 1024, replicas: 1 },
  worker: { cpu: 512, memory: 1024, replicas: 1 },
  logsStorageSize: 10,
  dagsStorageSize: 5,
  executor: 'LocalExecutor',
}

// Aurora数据库配置
database: {
  engine: 'aurora-postgresql',
  engineVersion: '15.4',
  serverlessV2Scaling: { minCapacity: 0.5, maxCapacity: 2 },
  backupRetention: 7,
  deletionProtection: false,
  readerInstances: 1,
  enablePerformanceInsights: true,
}
```

## 🔐 安全增强

### 新增安全特性
- Aurora自动密码轮换
- EFS加密存储
- Secrets Manager集成
- Service Connect内部通信加密
- 最小权限IAM角色

## 📈 性能优化

### 自动扩缩容
- Aurora Serverless v2自动扩缩容
- ECS服务自动扩缩容
- Airflow Worker水平扩展

### 缓存策略
- Redis会话缓存
- Aurora读副本负载均衡
- CloudFront CDN加速

## 🔄 迁移策略

### 数据迁移
1. 创建Aurora集群
2. 使用pg_dump/pg_restore迁移数据
3. 验证数据完整性
4. 切换应用连接
5. 停用旧的PostgreSQL容器

### 服务迁移
1. 部署新的中间件服务
2. 更新应用配置
3. 逐步切换流量
4. 验证功能正常
5. 清理旧资源

## 📝 运维指南

### 日常维护
- Aurora自动维护窗口
- RabbitMQ集群健康检查
- Airflow DAG监控
- EFS存储使用监控

### 故障排除
- Service Connect连接问题
- Aurora性能调优
- RabbitMQ队列堆积
- Airflow任务失败处理

---

## 总结

本次架构升级显著提升了系统的现代化程度和运维效率：

✅ **可靠性提升**: Aurora自动故障转移，RabbitMQ高可用  
✅ **可扩展性增强**: Serverless自动扩缩容，水平扩展支持  
✅ **运维简化**: 托管服务减少运维负担，自动化监控告警  
✅ **成本优化**: 按需付费模式，资源利用率提升  
✅ **安全加固**: 多层安全防护，自动密码管理  

升级后的架构为元晖业务的长期发展奠定了坚实的技术基础。
