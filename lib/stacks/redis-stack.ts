import * as cdk from 'aws-cdk-lib';
import * as ec2 from 'aws-cdk-lib/aws-ec2';
import * as ecs from 'aws-cdk-lib/aws-ecs';
import * as logs from 'aws-cdk-lib/aws-logs';
import * as servicediscovery from 'aws-cdk-lib/aws-servicediscovery';
import { Construct } from 'constructs';
import { EnvironmentConfig } from '../config';

export interface RedisStackProps extends cdk.StackProps {
  config: EnvironmentConfig;
  vpc: ec2.Vpc;
  cluster: ecs.Cluster;
  serviceConnectNamespace: servicediscovery.HttpNamespace;
}

/**
 * Redis基础设施栈
 * 基于ECS EC2部署的单实例Redis缓存服务，使用持久化存储
 */
export class RedisStack extends cdk.Stack {
  public readonly redisEndpoint: string;
  public readonly redisPort: number = 6379;
  public readonly redisService: ecs.Ec2Service;

  constructor(scope: Construct, id: string, props: RedisStackProps) {
    super(scope, id, props);

    const { config, vpc, cluster, serviceConnectNamespace } = props;

    // 获取Redis安全组（从网络栈导入）
    const redisSecurityGroup = ec2.SecurityGroup.fromSecurityGroupId(
      this,
      'ImportedRedisSecurityGroup',
      cdk.Fn.importValue(`${config.environment}-redis-security-group-id`)
    );

    // 获取ECS安全组（从网络栈导入）
    const ecsSecurityGroup = ec2.SecurityGroup.fromSecurityGroupId(
      this,
      'ImportedEcsSecurityGroup',
      cdk.Fn.importValue(`${config.environment}-ecs-security-group-id`)
    );

    // 创建Redis单实例任务定义（EC2模式）
    const redisTaskDefinition = new ecs.Ec2TaskDefinition(this, 'RedisTaskDefinition', {
      family: `yuanhui-redis-${config.environment}`,
      networkMode: ecs.NetworkMode.BRIDGE,
    });

    // 添加宿主机volume用于Redis数据持久化
    redisTaskDefinition.addVolume({
      name: 'redis-data',
      host: {
        sourcePath: '/data/redis',
      },
    });

    // 创建Redis单实例容器
    const redisContainer = redisTaskDefinition.addContainer('RedisContainer', {
      image: ecs.ContainerImage.fromRegistry('redis:7.2-alpine'),
      memoryReservationMiB: config.redis.memory || 256,
      command: [
        'redis-server',
        '--dir', '/data',                    // 数据目录
        '--appendonly', 'yes',               // 启用AOF持久化
        '--appendfsync', 'everysec',         // AOF同步策略
        '--save', '900 1',                   // RDB快照：900秒内至少1个key变化
        '--save', '300 10',                  // RDB快照：300秒内至少10个key变化
        '--save', '60 10000',                // RDB快照：60秒内至少10000个key变化
        '--maxmemory', '200mb',              // 设置最大内存使用
        '--maxmemory-policy', 'allkeys-lru', // 内存满时使用LRU策略
        '--tcp-keepalive', '300',            // 增加TCP keepalive间隔到5分钟
        '--timeout', '0',                    // 客户端连接超时（0=禁用）
        '--tcp-backlog', '511',              // TCP监听队列长度
        '--databases', '16',                 // 设置数据库数量
        '--loglevel', 'notice',              // 设置日志级别
        '--bind', '0.0.0.0',                 // 绑定所有接口
        '--port', '6379',                    // Redis端口
        '--protected-mode', 'no',            // 禁用保护模式
      ],
      environment: {
        REDIS_MODE: 'standalone',
      },
      logging: ecs.LogDrivers.awsLogs({
        streamPrefix: `redis`,
        logGroup: new logs.LogGroup(this, 'RedisLogGroup', {
          logGroupName: `/aws/ecs/yuanhui-redis-${config.environment}`,
          retention: logs.RetentionDays.ONE_WEEK,
          removalPolicy: cdk.RemovalPolicy.DESTROY,
        }),
      }),
      healthCheck: {
        command: ['CMD-SHELL', 'redis-cli ping || exit 1'],
        interval: cdk.Duration.seconds(30),
        timeout: cdk.Duration.seconds(5),
        retries: 3,
        startPeriod: cdk.Duration.seconds(15),
      },
    });

    // 挂载Redis数据volume
    redisContainer.addMountPoints({
      sourceVolume: 'redis-data',
      containerPath: '/data',
      readOnly: false,
    });

    // 添加Redis端口映射
    redisContainer.addPortMappings({
      name: 'redis',
      containerPort: 6379,
      hostPort: 0, // 动态端口映射
      protocol: ecs.Protocol.TCP,
    });

    // 创建Redis单实例服务（使用REPLICA调度策略）
    this.redisService = new ecs.Ec2Service(this, 'RedisService', {
      cluster,
      taskDefinition: redisTaskDefinition,
      serviceName: `yuanhui-redis-${config.environment}`,
      desiredCount: 1, // 单实例模式
      enableExecuteCommand: true,
      serviceConnectConfiguration: {
        namespace: serviceConnectNamespace.namespaceName,
        services: [
          {
            portMappingName: 'redis',
            dnsName: 'redis',
            port: 6379,
          },
        ],
      },
      placementStrategies: [
        ecs.PlacementStrategy.spreadAcrossInstances(), // 在实例间分散部署
      ],
      placementConstraints: [
        ecs.PlacementConstraint.memberOf('attribute:ecs.instance-type =~ t3.*'), // 限制在t3实例类型上运行
      ],
    });

    // 设置Redis端点
    this.redisEndpoint = 'redis'; // 使用Service Connect DNS名称

    // 输出Redis信息
    new cdk.CfnOutput(this, 'RedisEndpoint', {
      value: this.redisEndpoint,
      description: 'Redis endpoint for application connections',
      exportName: `${config.environment}-redis-endpoint`,
    });

    new cdk.CfnOutput(this, 'RedisPort', {
      value: this.redisPort.toString(),
      description: 'Redis port',
      exportName: `${config.environment}-redis-port`,
    });

    new cdk.CfnOutput(this, 'RedisServiceName', {
      value: this.redisService.serviceName,
      description: 'Redis ECS service name',
      exportName: `${config.environment}-redis-service-name`,
    });

    // 添加标签
    cdk.Tags.of(this).add('Environment', config.environment);
    cdk.Tags.of(this).add('Project', 'YuanhuiOdoo');
    cdk.Tags.of(this).add('Stack', 'Redis');
  }


}
