import * as cdk from 'aws-cdk-lib';
import * as ecs from 'aws-cdk-lib/aws-ecs';
import * as ec2 from 'aws-cdk-lib/aws-ec2';
import * as elbv2 from 'aws-cdk-lib/aws-elasticloadbalancingv2';
import * as logs from 'aws-cdk-lib/aws-logs';
import { Construct } from 'constructs';
import { EnvironmentConfig } from '../config';
import { AuroraDatabaseStack } from './aurora-database-stack';
import { RedisStack } from './redis-stack';
import { LoadBalancerStack } from './load-balancer-stack';
// import { DnsStack } from './dns-stack'; // DNS由第三方管理
import * as servicediscovery from 'aws-cdk-lib/aws-servicediscovery';

export interface ApplicationStackProps extends cdk.StackProps {
  config: EnvironmentConfig;
  vpc: ec2.Vpc;
  cluster: ecs.Cluster;
  databaseStack: AuroraDatabaseStack;
  redisStack: RedisStack;
  loadBalancerStack: LoadBalancerStack;
  // dnsStack?: DnsStack; // DNS由第三方管理
  serviceConnectNamespace?: servicediscovery.HttpNamespace;
}

/**
 * 应用服务栈
 * 包含Odoo应用服务、cron任务服务，专注于业务逻辑
 * 现在自管理负载均衡器的目标组和路由规则
 */
export class ApplicationStack extends cdk.Stack {
  public readonly yherpService: ecs.Ec2Service;
  public readonly khmallService: ecs.Ec2Service;
  public readonly cronService: ecs.Ec2Service;
  public readonly appSecurityGroup: ec2.SecurityGroup;
  public yherpTargetGroup: elbv2.ApplicationTargetGroup;
  public yherpLongpollingTargetGroup: elbv2.ApplicationTargetGroup;
  public khmallTargetGroup: elbv2.ApplicationTargetGroup;

  constructor(scope: Construct, id: string, props: ApplicationStackProps) {
    super(scope, id, props);

    const { config, vpc, cluster, databaseStack, redisStack, loadBalancerStack, serviceConnectNamespace } = props;

    // 现在由ApplicationStack自管理目标组和路由规则

    // 创建任务定义
    const yherpTaskDefinition = this.createOdooTaskDefinition('YherpTask', config, databaseStack, redisStack, 'yherp');
    const khmallTaskDefinition = this.createOdooTaskDefinition('KhmallTask', config, databaseStack, redisStack, 'khmall');
    const cronTaskDefinition = this.createCronTaskDefinition('CronTask', config, databaseStack, redisStack);

    // 创建应用服务安全组
    this.appSecurityGroup = new ec2.SecurityGroup(this, 'ApplicationSecurityGroup', {
      vpc,
      description: 'Security group for Odoo applications',
      allowAllOutbound: true,
    });

    // 负载均衡器安全组规则现在由LoadBalancerStack管理

    // 创建ECS服务（在BRIDGE网络模式下，安全组由ECS集群的EC2实例管理）
    this.yherpService = new ecs.Ec2Service(this, 'YherpService', {
      cluster,
      taskDefinition: yherpTaskDefinition,
      serviceName: `yherp-${config.environment}`,
      serviceConnectConfiguration: serviceConnectNamespace ? {
        namespace: serviceConnectNamespace.namespaceName,
        services: [{
          portMappingName: 'yherp-web',
          dnsName: 'yherp',
          port: 8069,
        }, {
          portMappingName: 'yherp-longpolling',
          dnsName: 'yherp-longpolling',
          port: 8072,
        }],
      } : undefined,
    });

    this.khmallService = new ecs.Ec2Service(this, 'KhmallService', {
      cluster,
      taskDefinition: khmallTaskDefinition,
      serviceName: `khmall-${config.environment}`,
      serviceConnectConfiguration: serviceConnectNamespace ? {
        namespace: serviceConnectNamespace.namespaceName,
        services: [{
          portMappingName: 'khmall-web',
          dnsName: 'khmall',
          port: 8069,
        }, {
          portMappingName: 'khmall-longpolling',
          dnsName: 'khmall-longpolling',
          port: 8072,
        }],
      } : undefined,
    });

    this.cronService = new ecs.Ec2Service(this, 'CronService', {
      cluster,
      taskDefinition: cronTaskDefinition,
      desiredCount: 1, // Cron服务只需要一个实例
      serviceName: `odoo-cron-${config.environment}`,
      serviceConnectConfiguration: serviceConnectNamespace ? {
        namespace: serviceConnectNamespace.namespaceName,
        // Cron服务作为客户端，不提供服务
      } : undefined,
    });

    // 创建目标组并配置负载均衡器路由
    this.createTargetGroupsAndRouting(config, vpc, loadBalancerStack);

    // 配置自动扩容
    this.setupAutoScaling(this.yherpService, config);
    this.setupAutoScaling(this.khmallService, config);

    // 输出应用信息
    new cdk.CfnOutput(this, 'YherpServiceArn', {
      value: this.yherpService.serviceArn,
      description: 'Yherp ECS Service ARN',
      exportName: `${config.environment}-yherp-service-arn`,
    });

    new cdk.CfnOutput(this, 'KhmallServiceArn', {
      value: this.khmallService.serviceArn,
      description: 'Khmall ECS Service ARN',
      exportName: `${config.environment}-khmall-service-arn`,
    });

    // DNS记录由第三方管理，不在此处创建

    // 添加标签
    cdk.Tags.of(this).add('Environment', config.environment);
    cdk.Tags.of(this).add('Project', 'YuanhuiOdoo');
    cdk.Tags.of(this).add('Stack', 'Application');
  }

  /**
   * 创建目标组并配置负载均衡器路由
   */
  private createTargetGroupsAndRouting(
    config: EnvironmentConfig,
    vpc: ec2.Vpc,
    loadBalancerStack: LoadBalancerStack
  ): void {
    // 创建Yherp目标组
    this.yherpTargetGroup = new elbv2.ApplicationTargetGroup(this, 'YherpTargetGroup', {
      vpc,
      port: 8069,
      protocol: elbv2.ApplicationProtocol.HTTP,
      targetType: elbv2.TargetType.INSTANCE,
      targetGroupName: `yherp-tg-${config.environment}`,
      healthCheck: {
        enabled: true,
        healthyHttpCodes: '200,302',
        path: '/web/health',
        interval: cdk.Duration.seconds(30),
        timeout: cdk.Duration.seconds(5),
        healthyThresholdCount: 2,
        unhealthyThresholdCount: 3,
        protocol: elbv2.Protocol.HTTP,
      },
      stickinessCookieDuration: cdk.Duration.hours(24),
      stickinessCookieName: 'YHERP_SESSION',
    });

    // 创建Yherp Longpolling目标组
    this.yherpLongpollingTargetGroup = new elbv2.ApplicationTargetGroup(this, 'YherpLongpollingTargetGroup', {
      vpc,
      port: 8072,
      protocol: elbv2.ApplicationProtocol.HTTP,
      targetType: elbv2.TargetType.INSTANCE,
      targetGroupName: `yherp-longpoll-tg-${config.environment}`,
      healthCheck: {
        enabled: true,
        healthyHttpCodes: '200,404',
        path: '/longpolling/health',
        interval: cdk.Duration.seconds(30),
        timeout: cdk.Duration.seconds(5),
        healthyThresholdCount: 2,
        unhealthyThresholdCount: 3,
        protocol: elbv2.Protocol.HTTP,
      },
    });

    // 创建Khmall目标组
    this.khmallTargetGroup = new elbv2.ApplicationTargetGroup(this, 'KhmallTargetGroup', {
      vpc,
      port: 8069,
      protocol: elbv2.ApplicationProtocol.HTTP,
      targetType: elbv2.TargetType.INSTANCE,
      targetGroupName: `khmall-tg-${config.environment}`,
      healthCheck: {
        enabled: true,
        healthyHttpCodes: '200,302',
        path: '/web/health',
        interval: cdk.Duration.seconds(30),
        timeout: cdk.Duration.seconds(5),
        healthyThresholdCount: 2,
        unhealthyThresholdCount: 3,
        protocol: elbv2.Protocol.HTTP,
      },
      stickinessCookieDuration: cdk.Duration.hours(24),
      stickinessCookieName: 'KHMALL_SESSION',
    });

    // 配置负载均衡器路由规则
    // Yherp longpolling路由（优先级更高）
    if (loadBalancerStack.publicHttpListener) {
      new elbv2.ApplicationListenerRule(this, 'YherpLongpollingHttpRule', {
        listener: loadBalancerStack.publicHttpListener,
        priority: 90,
        conditions: [
          elbv2.ListenerCondition.pathPatterns(['/longpolling/*', '/websocket/*']),
          elbv2.ListenerCondition.hostHeaders([config.network.domains.applications.yherp.public, config.network.domains.applications.yherp.internal])
        ],
        action: elbv2.ListenerAction.forward([this.yherpLongpollingTargetGroup]),
      });
    }

    if (loadBalancerStack.publicHttpsListener) {
      new elbv2.ApplicationListenerRule(this, 'YherpLongpollingHttpsRule', {
        listener: loadBalancerStack.publicHttpsListener,
        priority: 90,
        conditions: [
          elbv2.ListenerCondition.pathPatterns(['/longpolling/*', '/websocket/*']),
          elbv2.ListenerCondition.hostHeaders([config.network.domains.applications.yherp.public, config.network.domains.applications.yherp.internal])
        ],
        action: elbv2.ListenerAction.forward([this.yherpLongpollingTargetGroup]),
      });
    }

    // Yherp主应用路由（公网）
    if (loadBalancerStack.publicHttpListener) {
      new elbv2.ApplicationListenerRule(this, 'YherpPublicHttpRule', {
        listener: loadBalancerStack.publicHttpListener,
        priority: 100,
        conditions: [elbv2.ListenerCondition.hostHeaders([config.network.domains.applications.yherp.public])],
        action: elbv2.ListenerAction.forward([this.yherpTargetGroup], {
          stickinessDuration: cdk.Duration.hours(24),
        }),
      });
    }

    if (loadBalancerStack.publicHttpsListener) {
      new elbv2.ApplicationListenerRule(this, 'YherpPublicHttpsRule', {
        listener: loadBalancerStack.publicHttpsListener,
        priority: 100,
        conditions: [elbv2.ListenerCondition.hostHeaders([config.network.domains.applications.yherp.public])],
        action: elbv2.ListenerAction.forward([this.yherpTargetGroup], {
          stickinessDuration: cdk.Duration.hours(24),
        }),
      });
    }


    // Khmall应用路由
    if (loadBalancerStack.publicHttpListener) {
      new elbv2.ApplicationListenerRule(this, 'KhmallHttpRule', {
        listener: loadBalancerStack.publicHttpListener,
        priority: 200,
        conditions: [elbv2.ListenerCondition.hostHeaders([config.network.domains.applications.khmall.domain])],
        action: elbv2.ListenerAction.forward([this.khmallTargetGroup], {
          stickinessDuration: cdk.Duration.hours(24),
        }),
      });
    }

    if (loadBalancerStack.publicHttpsListener) {
      new elbv2.ApplicationListenerRule(this, 'KhmallHttpsRule', {
        listener: loadBalancerStack.publicHttpsListener,
        priority: 200,
        conditions: [elbv2.ListenerCondition.hostHeaders([config.network.domains.applications.khmall.domain])],
        action: elbv2.ListenerAction.forward([this.khmallTargetGroup], {
          stickinessDuration: cdk.Duration.hours(24),
        }),
      });
    }


    // 将ECS服务附加到目标组
    this.yherpService.attachToApplicationTargetGroup(this.yherpTargetGroup);
    this.yherpService.attachToApplicationTargetGroup(this.yherpLongpollingTargetGroup);
    this.khmallService.attachToApplicationTargetGroup(this.khmallTargetGroup);
  }

  /**
   * 创建Odoo应用任务定义
   */
  private createOdooTaskDefinition(
    id: string,
    config: EnvironmentConfig,
    databaseStack: AuroraDatabaseStack,
    redisStack: RedisStack,
    appName: string
  ): ecs.Ec2TaskDefinition {
    const taskDefinition = new ecs.Ec2TaskDefinition(this, id, {
      networkMode: ecs.NetworkMode.BRIDGE,
    });

    // 添加Odoo容器
    const container = taskDefinition.addContainer('OdooContainer', {
      image: ecs.ContainerImage.fromRegistry('odoo:16'),
      memoryReservationMiB: 2048,
      environment: {
        // 数据库连接配置（支持Aurora和RDS）
        HOST: databaseStack.clusterEndpoint.split(':')[0], // 数据库主机地址
        PORT: '5432', // PostgreSQL标准端口
        USER: 'odoo_admin',
        REDIS_HOST: redisStack.redisEndpoint,
        REDIS_PORT: redisStack.redisPort.toString(),
        ODOO_RC: `/etc/odoo/odoo.conf`,
        // Longpolling配置
        LONGPOLLING_PORT: '8072',
        WORKERS: '2', // 启用多进程模式以支持longpolling
      },
      secrets: {
        PASSWORD: ecs.Secret.fromSecretsManager(databaseStack.databaseSecret, 'password'),
      },
      logging: ecs.LogDrivers.awsLogs({
        streamPrefix: `${appName}-odoo`,
        logGroup: new logs.LogGroup(this, `${id}LogGroup`, {
          logGroupName: `/aws/ecs/${appName}-${config.environment}`,
          retention: logs.RetentionDays.ONE_MONTH,
          removalPolicy: cdk.RemovalPolicy.DESTROY,
        }),
      }),
      healthCheck: {
        command: ['CMD-SHELL', 'curl -f http://localhost:8069/web/health || exit 1'],
        interval: cdk.Duration.seconds(30),
        timeout: cdk.Duration.seconds(5),
        retries: 3,
        startPeriod: cdk.Duration.seconds(60),
      },
    });

    // 添加主应用端口映射
    container.addPortMappings({
      name: `${appName}-web`,
      containerPort: 8069,
      hostPort: 0, // 动态端口映射
      protocol: ecs.Protocol.TCP,
    });

    // 添加longpolling端口映射
    container.addPortMappings({
      name: `${appName}-longpolling`,
      containerPort: 8072,
      hostPort: 0, // 动态端口映射
      protocol: ecs.Protocol.TCP,
    });

    return taskDefinition;
  }

  /**
   * 创建Cron任务定义
   */
  private createCronTaskDefinition(
    id: string,
    config: EnvironmentConfig,
    databaseStack: AuroraDatabaseStack,
    redisStack: RedisStack
  ): ecs.Ec2TaskDefinition {
    const taskDefinition = new ecs.Ec2TaskDefinition(this, id, {
      networkMode: ecs.NetworkMode.BRIDGE,
    });

    // 添加Odoo Cron容器
    taskDefinition.addContainer('OdooCronContainer', {
      image: ecs.ContainerImage.fromRegistry('odoo:16'),
      command: ['odoo', '--max-cron-threads=2', '--no-http'],
      memoryReservationMiB: 2048,
      environment: {
        // 数据库连接配置（支持Aurora和RDS）
        HOST: databaseStack.clusterEndpoint.split(':')[0], // 数据库主机地址
        PORT: '5432', // PostgreSQL标准端口
        USER: 'odoo_admin',
        REDIS_HOST: redisStack.redisEndpoint,
        REDIS_PORT: redisStack.redisPort.toString(),
      },
      secrets: {
        PASSWORD: ecs.Secret.fromSecretsManager(databaseStack.databaseSecret, 'password'),
      },
      logging: ecs.LogDrivers.awsLogs({
        streamPrefix: 'odoo-cron',
        logGroup: new logs.LogGroup(this, `${id}LogGroup`, {
          logGroupName: `/aws/ecs/odoo-cron-${config.environment}`,
          retention: logs.RetentionDays.ONE_MONTH,
          removalPolicy: cdk.RemovalPolicy.DESTROY,
        }),
      }),
    });

    return taskDefinition;
  }

  /**
   * 设置自动扩容
   */
  private setupAutoScaling(service: ecs.Ec2Service, config: EnvironmentConfig): void {
    const scaling = service.autoScaleTaskCount({
      minCapacity: 1,
      maxCapacity: 2,
    });

    // CPU使用率扩容策略
    scaling.scaleOnCpuUtilization('CpuScaling', {
      targetUtilizationPercent: 70,
      scaleInCooldown: cdk.Duration.minutes(5),
      scaleOutCooldown: cdk.Duration.minutes(2),
    });

    // 内存使用率扩容策略
    scaling.scaleOnMemoryUtilization('MemoryScaling', {
      targetUtilizationPercent: 80,
      scaleInCooldown: cdk.Duration.minutes(5),
      scaleOutCooldown: cdk.Duration.minutes(2),
    });
  }

  // 负载均衡器配置方法已移除，现在由LoadBalancerStack全权负责

  /**
   * DNS记录由第三方管理，不在此处创建
   * 需要手动配置以下域名指向负载均衡器：
   * - yh-dev.kh2u.com -> internal ALB (通过OpenZiti零信任网络)
   * - dp-dev.kh2u.com -> public ALB  
   * - j2mall.tw -> public ALB
   */
}
