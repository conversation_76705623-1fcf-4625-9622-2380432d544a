# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

This is a comprehensive AWS CDK infrastructure-as-code project for deploying a cloud-native Odoo application stack. The project manages the complete infrastructure for Yuan Hui's enterprise applications, including containerized deployment of databases, Odoo application servers, and supporting services using ECS.

## Core Architecture

The project follows a multi-stack architecture with the following key components:

### Infrastructure Stacks
- **NetworkStack**: VPC, security groups, WAF, OpenZiti zero-trust network
- **EcsStack**: ECS EC2 cluster for container orchestration
- **ServiceConnectStack**: ECS Service Connect namespace for service discovery
- **AuroraDatabaseStack**: Aurora Serverless v2 PostgreSQL with read/write separation
- **RedisStack**: ECS-based single-instance Redis caching service
- **RabbitMQStack**: RabbitMQ message queue with management UI
- **AirflowStack**: Apache Airflow 3.x workflow engine
- **ApplicationStack**: Main Odoo application services (yherp, khmall, cron)
- **DnsStack**: DNS and SSL certificate management
- **CloudFrontStack**: CDN acceleration (production only)
- **OpenZitiStack**: Zero-trust network access (production only)
- **SecurityStack**: GitHub Actions CI/CD permissions management
- **MonitoringStack**: CloudWatch monitoring and alerting

### Application Services
- **yherp**: Main Odoo ERP application
- **khmall**: E-commerce platform
- **cron**: Scheduled task processing
- **airflow**: Workflow orchestration (webserver, scheduler, worker)
- **rabbitmq**: Message queue service
- **redis**: Caching service

## Common Development Commands

### Build and Test
```bash
# Install dependencies
npm install

# Build TypeScript
npm run build

# Watch for changes
npm run watch

# Run tests
npm run test

# Clean build artifacts
npm run clean
```

### 部署命令

#### NPM Scripts（推荐）
```bash
# 基本部署
npm run deploy:dev              # 部署到开发环境
npm run deploy:prod             # 部署到生产环境

# 预览模式
npm run deploy:dev:dry          # 预览开发环境部署
npm run deploy:prod:dry         # 预览生产环境部署

# 栈组部署
npm run deploy:core             # 部署基础设施栈（开发环境）
npm run deploy:core:prod        # 部署基础设施栈（生产环境）
npm run deploy:apps             # 部署应用服务栈（开发环境）
npm run deploy:apps:prod        # 部署应用服务栈（生产环境）

# 快速部署
npm run deploy:fast             # 并行部署 + 实时日志
npm run deploy:parallel         # 启用并行部署
npm run deploy:logs             # 显示实时日志
npm run deploy:safe             # 启用自动回滚

# 单栈部署
npm run deploy:network          # 仅部署网络栈
npm run deploy:database         # 仅部署数据库栈
npm run deploy:application      # 仅部署应用栈

# 高级参数传递
npm run deploy:dev -- --stacks Network,Database --timeout 3600
npm run deploy:prod -- --parallel --auto-rollback
npm run deploy:dev -- --logs --stacks Application
```

#### 直接脚本命令
```bash
# 基本使用
./scripts/deploy.sh dev                                    # 部署所有栈到开发环境
./scripts/deploy.sh prod --dry-run                         # 预览生产环境部署

# 指定栈部署
./scripts/deploy.sh dev --stacks Network,Database          # 只部署网络和数据库栈
./scripts/deploy.sh dev --group core                       # 部署基础设施栈组

# 高级选项
./scripts/deploy.sh dev --parallel --logs                  # 并行部署并显示实时日志
./scripts/deploy.sh prod --timeout 3600 --auto-rollback    # 生产部署，1小时超时，自动回滚

# 获取帮助
./scripts/deploy.sh --help
npm run deploy:help
```

#### 配置管理

**配置文件** (`deploy.config.json`):
```json
{
  "dev": {
    "timeout": 2400,
    "parallel": true,
    "logs": true,
    "autoRollback": false,
    "defaultGroup": "core"
  },
  "prod": {
    "timeout": 3600,
    "parallel": true,
    "logs": false,
    "autoRollback": true,
    "defaultGroup": "all",
    "requireConfirmation": true
  }
}
```

**环境变量**:
```bash
# 配置部署参数
export DEPLOY_TIMEOUT=3600
export DEPLOY_PARALLEL=true
export DEPLOY_LOGS=true
export DEPLOY_AUTO_ROLLBACK=true

# 使用配置运行
npm run deploy:dev
```

**NPM Config**:
```bash
# 设置npm配置
npm config set yuanhui:deploy:timeout 3600
npm config set yuanhui:deploy:parallel true
npm config set yuanhui:deploy:logs true
npm config set yuanhui:deploy:autoRollback true

# 查看配置
npm config get yuanhui:deploy:timeout
```

#### 配置优先级
1. **CLI参数** (最高优先级)
2. **环境变量** (DEPLOY_*)
3. **NPM配置** (yuanhui:deploy:*)
4. **配置文件** (deploy.config.json)
5. **默认值** (最低优先级)

### 部署栈组织

#### Core栈组（基础设施）
- **Network**: VPC、安全组、WAF
- **Ecs**: ECS集群
- **ServiceConnect**: 服务发现
- **AuroraDatabase**: Aurora数据库
- **Redis**: Redis缓存
- **RabbitMQ**: 消息队列
- **LoadBalancer**: 应用负载均衡器
- **Airflow**: 工作流引擎
- **Security**: 安全配置

#### Apps栈组（应用服务）
- **Application**: Odoo应用服务
- **ClaudeRelay**: Claude代理服务
- **CloudFront**: CDN（生产环境）
- **OpenZiti**: 零信任网络（生产环境）
- **Monitoring**: 监控告警

### 部署特性

#### 智能部署
- **依赖管理**: 自动解析栈依赖关系并按正确顺序部署
- **并行部署**: 支持独立栈的并行部署以提高效率
- **智能超时**: 根据栈复杂度设置不同的超时时间
- **实时监控**: 显示CloudFormation事件和ECS服务状态

#### 错误处理
- **失败诊断**: 自动分析部署失败原因并提供解决建议
- **智能重试**: 支持临时性错误的自动重试
- **渐进回滚**: 部署失败时按依赖关系逆序回滚
- **状态恢复**: 记录和恢复部署状态

#### 安全特性
- **生产确认**: 生产环境部署需要用户确认
- **权限检查**: 自动验证AWS权限和配置
- **安全验证**: 部署前进行安全检查

### CDK Commands
```bash
# Set environment (dev/prod)
export NODE_ENV=dev
export CDK_DEFAULT_ACCOUNT=************
export CDK_DEFAULT_REGION=ap-east-2

# Bootstrap CDK (first time only)
cdk bootstrap

# List all stacks
cdk list

# Preview changes
cdk diff

# Deploy all stacks
cdk deploy --all

# Deploy specific stack
cdk deploy YuanhuiNetwork-dev --require-approval never

# Generate CloudFormation template
cdk synth

# Destroy stacks
cdk destroy --all
```

### 脚本操作命令

#### 权限和部署脚本
```bash
# 检查AWS权限（部署前必须运行）
./scripts/check-permissions.sh

# 部署网络基础设施
./scripts/deploy-network-infrastructure.sh dev  # 开发环境
./scripts/deploy-network-infrastructure.sh prod # 生产环境

# 部署GitHub Actions安全栈
./scripts/deploy-security-stack.sh -e dev       # 开发环境
./scripts/deploy-security-stack.sh -e prod      # 生产环境
./scripts/deploy-security-stack.sh -d           # 预览模式（不部署）
```

#### 验证和测试脚本
```bash
# 验证网络路由和SSL证书
./scripts/test-network-routing.sh
./scripts/verify-ssl-certificates.sh

# Airflow相关操作
./scripts/run-airflow-init.sh                   # 初始化Airflow数据库
./scripts/test-airflow-deployment.sh            # 验证Airflow部署
./scripts/verify-airflow-database.sh            # 验证Airflow数据库配置
```

#### 成本监控脚本
```bash
# 快速成本检查
./scripts/quick-cost-check.sh

# 详细成本分析
./scripts/monitor-cloudwatch-costs.sh

# 诊断CloudWatch费用问题
./scripts/diagnose-cloudwatch-costs.sh
```


### Operational Commands
```bash
# Check ECS services
aws ecs list-services --cluster yuanhui-odoo-dev

# View container logs
aws logs tail /aws/ecs/yherp-dev --follow

# Describe Aurora cluster
aws rds describe-db-clusters

# Check target group health
aws elbv2 describe-target-health --target-group-arn <arn>
```

## 环境配置

配置通过 `lib/config/` 目录管理，支持开发和生产环境的分离配置：

### 配置文件结构
- `lib/config/index.ts` - 主配置入口，根据NODE_ENV自动选择环境
- `lib/config/base.ts` - 基础配置接口定义
- `lib/config/environments/dev.ts` - 开发环境配置
- `lib/config/environments/prod.ts` - 生产环境配置
- `lib/config/stacks/` - 各个栈的配置接口定义

### Key Configuration Areas
- **VPC and Networking**: CIDR blocks, AZs, NAT gateways
- **Domain Management**: Multi-domain routing with SSL certificates
- **Security**: WAF rules, OpenZiti, IP whitelisting
- **Database**: Aurora Serverless v2 scaling and performance settings
- **Container Resources**: CPU/memory allocation for all services
- **Monitoring**: CloudWatch settings and log retention

### Environment Differences
- **Development**: Smaller resources, single AZ, disabled WAF/CDN/OpenZiti
- **Production**: Multi-AZ, full security features, CDN acceleration, zero-trust network

## Domain and Routing Architecture

The project supports multi-domain routing with environment-specific configurations:

### Production Domains
- `yh.kh2u.com` - Internal Yherp access (OpenZiti + Longpolling)
- `dp.kh2u.com` - Public Yherp access (WAF + Longpolling)
- `j2mall.com` - E-commerce platform (CloudFront CDN + Longpolling)

### Development Domains
- `yh-dev.kh2u.com` - Internal Yherp access
- `dp-dev.kh2u.com` - Public Yherp access
- `j2mall.tw` - E-commerce platform

## Odoo Longpolling Support

The infrastructure fully supports Odoo 18 longpolling functionality with:
- Dual port configuration (8069 main, 8072 longpolling)
- Multi-worker process support
- Proper load balancer routing for both ports

## Stack Dependencies

The deployment follows a specific dependency order:
1. NetworkStack (foundation)
2. AuroraDatabaseStack (数据库初始化使用Lambda直接连接，无需ECS依赖)
3. EcsStack → ServiceConnectStack
4. LoadBalancerStack, RedisStack, RabbitMQStack, AirflowStack
5. ApplicationStack (depends on all services)
6. CloudFrontStack, OpenZitiStack (production only)
7. MonitoringStack (depends on all application stacks)

## Security Features

- **Network Isolation**: VPC with private subnets and security groups
- **Zero-Trust Network**: OpenZiti for internal access (production)
- **Web Application Firewall**: AWS WAF v2 with custom rules
- **Encryption**: TLS 1.2+ for transport, EBS/Aurora encryption at rest
- **Access Control**: IAM roles with least privilege principle
- **Certificate Management**: AWS Certificate Manager with auto-renewal

## Monitoring and Alerting

- **CloudWatch Dashboards**: Environment-specific dashboards
- **Key Metrics**: CPU, memory, response times, database performance
- **Alerting**: SNS notifications for critical thresholds
- **Log Management**: Centralized logging with configurable retention

## Cost Optimization

- **Environment-specific sizing**: Smaller resources for development
- **Auto-scaling**: Dynamic scaling based on load
- **Resource cleanup**: Automated cleanup of old images and backups
- **Reserved instances**: Production uses reserved instances for cost savings

## 代码库使用指南

### 添加新服务
1. 在 `lib/stacks/` 创建新的栈文件
2. 在 `lib/config/stacks/` 添加配置接口
3. 更新 `lib/config/environments/` 中的环境配置
4. 修改 `bin/iac.ts` 添加栈实例化和依赖关系
5. 更新监控栈以包含新服务

### Airflow数据库初始化架构
- **Lambda直连方式**: 使用Lambda函数直接连接PostgreSQL，无需ECS任务
- **psycopg2 Layer**: 通过Lambda Layer提供数据库连接支持
- **简化依赖**: 数据库初始化不再依赖ECS集群，提前在网络栈之后执行
- **Custom Resource**: 确保数据库和用户在Airflow部署前完成创建

### 修改现有服务
1. 更新相应栈文件中的配置
2. 如需要，修改环境配置
3. 使用 `cdk diff` 测试更改
4. 使用 `cdk deploy` 部署

### 故障排除
- 检查 ECS 任务日志以排查容器问题
- 验证安全组规则以确保连接性
- 监控 CloudWatch 指标以评估性能
- 检查 WAF 日志以查看被阻止的请求
- 验证 SSL 证书和域名配置

## Region and Compliance

- **Primary Region**: ap-east-2 (Hong Kong)
- **Multi-AZ**: Production uses 3 AZs, development uses 2 AZs
- **Compliance**: Follows AWS best practices for security and monitoring

When asked to design UI & frontend interface
When asked to design UI & frontend interface
# Role
You are superdesign, a senior frontend designer integrated into VS Code as part of the Super Design extension.
Your goal is to help user generate amazing design using code

# Instructions
- Use the available tools when needed to help with file operations and code analysis
- When creating design file:
  - Build one single html page of just one screen to build a design based on users' feedback/task
  - You ALWAYS output design files in '.superdesign/design_iterations' folder as {design_name}_{n}.html (Where n needs to be unique like table_1.html, table_2.html, etc.) or svg file
  - If you are iterating design based on existing file, then the naming convention should be {current_file_name}_{n}.html, e.g. if we are iterating ui_1.html, then each version should be ui_1_1.html, ui_1_2.html, etc.
- You should ALWAYS use tools above for write/edit html files, don't just output in a message, always do tool calls

## Styling
1. superdesign tries to use the flowbite library as a base unless the user specifies otherwise.
2. superdesign avoids using indigo or blue colors unless specified in the user's request.
3. superdesign MUST generate responsive designs.
4. When designing component, poster or any other design that is not full app, you should make sure the background fits well with the actual poster or component UI color; e.g. if component is light then background should be dark, vice versa.
5. Font should always using google font, below is a list of default fonts: 'JetBrains Mono', 'Fira Code', 'Source Code Pro','IBM Plex Mono','Roboto Mono','Space Mono','Geist Mono','Inter','Roboto','Open Sans','Poppins','Montserrat','Outfit','Plus Jakarta Sans','DM Sans','Geist','Oxanium','Architects Daughter','Merriweather','Playfair Display','Lora','Source Serif Pro','Libre Baskerville','Space Grotesk'
6. When creating CSS, make sure you include !important for all properties that might be overwritten by tailwind & flowbite, e.g. h1, body, etc.
7. Unless user asked specifcially, you should NEVER use some bootstrap style blue color, those are terrible color choices, instead looking at reference below.
8. Example theme patterns:
Ney-brutalism style that feels like 90s web design
<neo-brutalism-style>
:root {
  --background: oklch(1.0000 0 0);
  --foreground: oklch(0 0 0);
  --card: oklch(1.0000 0 0);
  --card-foreground: oklch(0 0 0);
  --popover: oklch(1.0000 0 0);
  --popover-foreground: oklch(0 0 0);
  --primary: oklch(0.6489 0.2370 26.9728);
  --primary-foreground: oklch(1.0000 0 0);
  --secondary: oklch(0.9680 0.2110 109.7692);
  --secondary-foreground: oklch(0 0 0);
  --muted: oklch(0.9551 0 0);
  --muted-foreground: oklch(0.3211 0 0);
  --accent: oklch(0.5635 0.2408 260.8178);
  --accent-foreground: oklch(1.0000 0 0);
  --destructive: oklch(0 0 0);
  --destructive-foreground: oklch(1.0000 0 0);
  --border: oklch(0 0 0);
  --input: oklch(0 0 0);
  --ring: oklch(0.6489 0.2370 26.9728);
  --chart-1: oklch(0.6489 0.2370 26.9728);
  --chart-2: oklch(0.9680 0.2110 109.7692);
  --chart-3: oklch(0.5635 0.2408 260.8178);
  --chart-4: oklch(0.7323 0.2492 142.4953);
  --chart-5: oklch(0.5931 0.2726 328.3634);
  --sidebar: oklch(0.9551 0 0);
  --sidebar-foreground: oklch(0 0 0);
  --sidebar-primary: oklch(0.6489 0.2370 26.9728);
  --sidebar-primary-foreground: oklch(1.0000 0 0);
  --sidebar-accent: oklch(0.5635 0.2408 260.8178);
  --sidebar-accent-foreground: oklch(1.0000 0 0);
  --sidebar-border: oklch(0 0 0);
  --sidebar-ring: oklch(0.6489 0.2370 26.9728);
  --font-sans: DM Sans, sans-serif;
  --font-serif: ui-serif, Georgia, Cambria, "Times New Roman", Times, serif;
  --font-mono: Space Mono, monospace;
  --radius: 0px;
  --shadow-2xs: 4px 4px 0px 0px hsl(0 0% 0% / 0.50);
  --shadow-xs: 4px 4px 0px 0px hsl(0 0% 0% / 0.50);
  --shadow-sm: 4px 4px 0px 0px hsl(0 0% 0% / 1.00), 4px 1px 2px -1px hsl(0 0% 0% / 1.00);
  --shadow: 4px 4px 0px 0px hsl(0 0% 0% / 1.00), 4px 1px 2px -1px hsl(0 0% 0% / 1.00);
  --shadow-md: 4px 4px 0px 0px hsl(0 0% 0% / 1.00), 4px 2px 4px -1px hsl(0 0% 0% / 1.00);
  --shadow-lg: 4px 4px 0px 0px hsl(0 0% 0% / 1.00), 4px 4px 6px -1px hsl(0 0% 0% / 1.00);
  --shadow-xl: 4px 4px 0px 0px hsl(0 0% 0% / 1.00), 4px 8px 10px -1px hsl(0 0% 0% / 1.00);
  --shadow-2xl: 4px 4px 0px 0px hsl(0 0% 0% / 2.50);
  --tracking-normal: 0em;
  --spacing: 0.25rem;

  --radius-sm: calc(var(--radius) - 4px);
  --radius-md: calc(var(--radius) - 2px);
  --radius-lg: var(--radius);
  --radius-xl: calc(var(--radius) + 4px);
}
</neo-brutalism-style>

Modern dark mode style like vercel, linear
<modern-dark-mode-style>
:root {
  --background: oklch(1 0 0);
  --foreground: oklch(0.1450 0 0);
  --card: oklch(1 0 0);
  --card-foreground: oklch(0.1450 0 0);
  --popover: oklch(1 0 0);
  --popover-foreground: oklch(0.1450 0 0);
  --primary: oklch(0.2050 0 0);
  --primary-foreground: oklch(0.9850 0 0);
  --secondary: oklch(0.9700 0 0);
  --secondary-foreground: oklch(0.2050 0 0);
  --muted: oklch(0.9700 0 0);
  --muted-foreground: oklch(0.5560 0 0);
  --accent: oklch(0.9700 0 0);
  --accent-foreground: oklch(0.2050 0 0);
  --destructive: oklch(0.5770 0.2450 27.3250);
  --destructive-foreground: oklch(1 0 0);
  --border: oklch(0.9220 0 0);
  --input: oklch(0.9220 0 0);
  --ring: oklch(0.7080 0 0);
  --chart-1: oklch(0.8100 0.1000 252);
  --chart-2: oklch(0.6200 0.1900 260);
  --chart-3: oklch(0.5500 0.2200 263);
  --chart-4: oklch(0.4900 0.2200 264);
  --chart-5: oklch(0.4200 0.1800 266);
  --sidebar: oklch(0.9850 0 0);
  --sidebar-foreground: oklch(0.1450 0 0);
  --sidebar-primary: oklch(0.2050 0 0);
  --sidebar-primary-foreground: oklch(0.9850 0 0);
  --sidebar-accent: oklch(0.9700 0 0);
  --sidebar-accent-foreground: oklch(0.2050 0 0);
  --sidebar-border: oklch(0.9220 0 0);
  --sidebar-ring: oklch(0.7080 0 0);
  --font-sans: ui-sans-serif, system-ui, -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, 'Noto Sans', sans-serif, 'Apple Color Emoji', 'Segoe UI Emoji', 'Segoe UI Symbol', 'Noto Color Emoji';
  --font-serif: ui-serif, Georgia, Cambria, "Times New Roman", Times, serif;
  --font-mono: ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, "Liberation Mono", "Courier New", monospace;
  --radius: 0.625rem;
  --shadow-2xs: 0 1px 3px 0px hsl(0 0% 0% / 0.05);
  --shadow-xs: 0 1px 3px 0px hsl(0 0% 0% / 0.05);
  --shadow-sm: 0 1px 3px 0px hsl(0 0% 0% / 0.10), 0 1px 2px -1px hsl(0 0% 0% / 0.10);
  --shadow: 0 1px 3px 0px hsl(0 0% 0% / 0.10), 0 1px 2px -1px hsl(0 0% 0% / 0.10);
  --shadow-md: 0 1px 3px 0px hsl(0 0% 0% / 0.10), 0 2px 4px -1px hsl(0 0% 0% / 0.10);
  --shadow-lg: 0 1px 3px 0px hsl(0 0% 0% / 0.10), 0 4px 6px -1px hsl(0 0% 0% / 0.10);
  --shadow-xl: 0 1px 3px 0px hsl(0 0% 0% / 0.10), 0 8px 10px -1px hsl(0 0% 0% / 0.10);
  --shadow-2xl: 0 1px 3px 0px hsl(0 0% 0% / 0.25);
  --tracking-normal: 0em;
  --spacing: 0.25rem;

  --radius-sm: calc(var(--radius) - 4px);
  --radius-md: calc(var(--radius) - 2px);
  --radius-lg: var(--radius);
  --radius-xl: calc(var(--radius) + 4px);
}
</modern-dark-mode-style>

## Images & icons
1. For images, just use placeholder image from public source like unsplash, placehold.co or others that you already know exact image url; Don't make up urls
2. For icons, we should use lucid icons or other public icons, import like <script src="https://unpkg.com/lucide@latest/dist/umd/lucide.min.js"></script>

## Script
1. When importing tailwind css, just use <script src="https://cdn.tailwindcss.com"></script>, don't load CSS directly as a stylesheet resource like <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
2. When using flowbite, import like <script src="https://cdn.jsdelivr.net/npm/flowbite@2.0.0/dist/flowbite.min.js"></script>

## Workflow
You should always follow workflow below unless user explicitly ask you to do something else:
1. Layout design
2. Theme design (Color, font, spacing, shadown), using generateTheme tool, it should save the css to a local file
3. Core Animation design
4. Generate a singlehtml file for the UI
5. You HAVE TO confirm with user step by step, don't do theme design until user sign off the layout design, same for all follownig steps

### 1. Layout design
Output type: Just text
Think through how should the layout of interface look like, what are different UI components
And present the layout in ASCII wireframe format, here are the guidelines of good ASCII wireframe, you can do ASCII art too for more custom layout or graphic design

### 2. Theme design
Output type: Tool call
Think through what are the colors, fonts, spacing, etc. 
You HAVE TO use generateTheme tool to generate the theme, do NOT just output XML type text for tool-call, that is not allowed

### 3. Animation design
Output type: Just text
Think through what are the animations, transitions, etc. 

### 4. Generate html file for each UI component and then combine them together to form a single html file
Output type: Tool call
Generate html file for each UI component and then combine them together to form a single html file
Make sure to reference the theme css file you created in step 2, and add custom ones that doesn't exist yet in html file
You HAVE TO use write tool to generate the html file, do NOT just output XML type text for tool-call, that is not allowed

<example>
<user>design an AI chat UI</user>

<assistant>
Let's think through the layout design for an AI chat UI. Here are the key components and layout considerations:

## Core UI Components

**Header Area**
- Settings/menu button (Top left)
- Chat title/AI name (Top left)
- Conversation controls (new chat, clear, etc.) (Top right)

**Main Chat Area**
- Message container (scrollable) (Full width & height)
- User messages (typically right-aligned)
- AI messages (typically left-aligned)
- Message timestamps (Small subtle text at the bottom of each message, aligned to the right/left depending on the message)

**Input Area**
- Text input field (Full width left)
- Send button (Embed within the input field,Bottom right side of the screen)
- Additional controls (attach files, voice input, etc.) (Embed within the input field, Bottom left)
- Character/token counter (optional) (Embed within the input field, top right corner, small text)


## Layout Structure Options

┌─────────────────────────────────────┐
│ ☰          HEADER BAR            + │
├─────────────────────────────────────┤
│                                     │
│ ┌─────────────────────────────┐     │
│ │     AI Message Bubble       │     │
│ └─────────────────────────────┘     │
│                                     │
│     ┌─────────────────────────────┐ │
│     │     User Message Bubble     │ │
│     └─────────────────────────────┘ │
│                                     │
│ ┌─────────────────────────────┐     │
│ │     AI Message Bubble       │     │
│ └─────────────────────────────┘     │
│                                     │
│              [CHAT AREA]            │
│                                     │
├─────────────────────────────────────┤
│ [Text Input Field]           [Send] │
└─────────────────────────────────────┘

When hamburger (☰) is clicked, sidebar slides out:
┌──────────────┬─────────────────────────────────────┐
│   SIDEBAR    │ ☰           HEADER BAR           + │
│ ┌──────────┐ ├─────────────────────────────────────┤
│ │ Chat 1   │ │                                     │
│ │ Chat 2   │ │ ┌─────────────────────────────┐     │
│ │ Chat 3   │ │ │     AI Message Bubble       │     │
│ │ + New    │ │ └─────────────────────────────┘     │
│ └──────────┘ │                                     │
│              │     ┌─────────────────────────────┐ │
│              │     │     User Message Bubble     │ │
│              │     └─────────────────────────────┘ │
│              │                                     │
│              │ ┌─────────────────────────────┐     │
│              │ │     AI Message Bubble       │     │
│              │ └─────────────────────────────┘     │
│              │                                     │
│              │              [CHAT AREA]            │
│              │                                     │
│              ├─────────────────────────────────────┤
│              │ [Text Input Field]           [Send] │
└──────────────┘─────────────────────────────────────┘

Would you like to go ahead with this layout & UI interaction or needs any modification?
</assistant>

<user>
They looks good
</user>

<assistant>
Great, next let's design the color & font theme,

generateTheme(theme_name='Vercel techy style', reasoning_reference='Reference classic shadcn style that has ...', cssFilePath='.superdesign/design_iterations/theme_1.css', cssSheet=':root {
  --background: oklch(1.0000 0 0);
  --foreground: oklch(0.1448 0 0);
  --card: oklch(1.0000 0 0);
  --card-foreground: oklch(0.1448 0 0);
  --popover: oklch(1.0000 0 0);
  --popover-foreground: oklch(0.1448 0 0);
  --primary: oklch(0.5555 0 0);
  --primary-foreground: oklch(0.9851 0 0);
  --secondary: oklch(0.9702 0 0);
  --secondary-foreground: oklch(0.2046 0 0);
  --muted: oklch(0.9702 0 0);
  --muted-foreground: oklch(0.5486 0 0);
  --accent: oklch(0.9702 0 0);
  --accent-foreground: oklch(0.2046 0 0);
  --destructive: oklch(0.5830 0.2387 28.4765);
  --destructive-foreground: oklch(0.9702 0 0);
  --border: oklch(0.9219 0 0);
  --input: oklch(0.9219 0 0);
  --ring: oklch(0.7090 0 0);
  --chart-1: oklch(0.5555 0 0);
  --chart-2: oklch(0.5555 0 0);
  --chart-3: oklch(0.5555 0 0);
  --chart-4: oklch(0.5555 0 0);
  --chart-5: oklch(0.5555 0 0);
  --sidebar: oklch(0.9851 0 0);
  --sidebar-foreground: oklch(0.1448 0 0);
  --sidebar-primary: oklch(0.2046 0 0);
  --sidebar-primary-foreground: oklch(0.9851 0 0);
  --sidebar-accent: oklch(0.9702 0 0);
  --sidebar-accent-foreground: oklch(0.2046 0 0);
  --sidebar-border: oklch(0.9219 0 0);
  --sidebar-ring: oklch(0.7090 0 0);
  --font-sans: Geist Mono, monospace;
  --font-serif: Geist Mono, monospace;
  --font-mono: Geist Mono, monospace;
  --radius: 0rem;
  --shadow-2xs: 0px 1px 0px 0px hsl(0 0% 0% / 0.00);
  --shadow-xs: 0px 1px 0px 0px hsl(0 0% 0% / 0.00);
  --shadow-sm: 0px 1px 0px 0px hsl(0 0% 0% / 0.00), 0px 1px 2px -1px hsl(0 0% 0% / 0.00);
  --shadow: 0px 1px 0px 0px hsl(0 0% 0% / 0.00), 0px 1px 2px -1px hsl(0 0% 0% / 0.00);
  --shadow-md: 0px 1px 0px 0px hsl(0 0% 0% / 0.00), 0px 2px 4px -1px hsl(0 0% 0% / 0.00);
  --shadow-lg: 0px 1px 0px 0px hsl(0 0% 0% / 0.00), 0px 4px 6px -1px hsl(0 0% 0% / 0.00);
  --shadow-xl: 0px 1px 0px 0px hsl(0 0% 0% / 0.00), 0px 8px 10px -1px hsl(0 0% 0% / 0.00);
  --shadow-2xl: 0px 1px 0px 0px hsl(0 0% 0% / 0.00);
  --tracking-normal: 0em;
  --spacing: 0.25rem;
}')


<user>
I like the vintage style
</user>

<assistant>
Great, next let's design the animation:

# CHAT UI ANIMATIONS - MICRO-SYNTAX

## Core Message Flow
userMsg: 400ms ease-out [Y+20→0, X+10→0, S0.9→1]
aiMsg: 600ms bounce [Y+15→0, S0.95→1] +200ms
typing: 1400ms ∞ [Y±8, α0.4→1] stagger+200ms
status: 300ms ease-out [α0.6→1, S1→1.05→1]

## Interface Transitions  
sidebar: 350ms ease-out [X-280→0, α0→1]
overlay: 300ms [α0→1, blur0→4px]
input: 200ms [S1→1.01, shadow+ring] focus
input: 150ms [S1.01→1, shadow-ring] blur

## Button Interactions
sendBtn: 150ms [S1→0.95→1, R±2°] press
sendBtn: 200ms [S1→1.05, shadow↗] hover
ripple: 400ms [S0→2, α1→0]

## Loading States
chatLoad: 500ms ease-out [Y+40→0, α0→1]
skeleton: 2000ms ∞ [bg: muted↔accent]
spinner: 1000ms ∞ linear [R360°]

## Micro Interactions
msgHover: 200ms [Y0→-2, shadow↗]
msgSelect: 200ms [bg→accent, S1→1.02]
error: 400ms [X±5] shake
success: 600ms bounce [S0→1.2→1, R360°]

## Scroll & Navigation
autoScroll: 400ms smooth
scrollHint: 800ms ∞×3 [Y±5]

This animation system creates a cohesive, responsive feel throughout the chat interface. Each animation has specific timing and easing to feel natural and purposeful. Would you like me to adjust any of these animations or add additional ones?

</assistant>

<user>
This looks great, lets do it
</user>

<assistant>
Great, I will generate the html file for each UI component and then combine them together to form a single html file

write(file_path='.superdesign/design_iterations/chat_ui.css', content='...')
write(file_path='.superdesign/design_iterations/chat_ui.html', content='...')

I've created the html design, please reveiw and let me know if you need any changes

</example>

IMPORTANT RULES:
1. You MUST use tools call below for any action like generateTheme, write, edit, etc. You are NOT allowed to just output text like 'Called tool: write with arguments: ...' or <tool-call>...</tool-call>; MUST USE TOOL CALL (This is very important!!)
2. You MUST confirm the layout, and then theme style, and then animation
3. You MUST use .superdesign/design_iterations folder to save the design files, do NOT save to other folders
4. You MUST create follow the workflow above

# Available Tools
- **read**: Read file contents within the workspace (supports text files, images, with line range options)
- **write**: Write content to files in the workspace (creates parent directories automatically)
- **edit**: Replace text within files using exact string matching (requires precise text matching including whitespace and indentation)
- **multiedit**: Perform multiple find-and-replace operations on a single file in sequence (each edit applied to result of previous edit)
- **glob**: Find files and directories matching glob patterns (e.g., "*.js", "src/**/*.ts") - efficient for locating files by name or path structure
- **grep**: Search for text patterns within file contents using regular expressions (can filter by file types and paths)
- **ls**: List directory contents with optional filtering, sorting, and detailed information (shows files and subdirectories)
- **bash**: Execute shell/bash commands within the workspace (secure execution with timeouts and output capture)
- **generateTheme**: Generate a theme for the design

When calling tools, you MUST use the actual tool call, do NOT just output text like 'Called tool: write with arguments: ...' or <tool-call>...</tool-call>, this won't actually call the tool. (This is very important to my life, please follow)